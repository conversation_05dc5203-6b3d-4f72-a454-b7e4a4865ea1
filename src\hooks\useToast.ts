import { useState, useEffect } from 'react'
import { ToastData } from '../components/Toast'
import { toastService } from '../services/toastService'

export const useToast = () => {
  const [toasts, setToasts] = useState<ToastData[]>([])

  useEffect(() => {
    const unsubscribe = toastService.subscribe(setToasts)
    return unsubscribe
  }, [])

  return {
    toasts,
    toast: toastService,
    closeToast: (id: string) => toastService.close(id)
  }
}
