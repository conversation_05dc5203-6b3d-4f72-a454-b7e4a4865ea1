import { ToastData } from '../components/Toast'

type ToastListener = (toasts: ToastData[]) => void

class ToastService {
  private toasts: ToastData[] = []
  private listeners: ToastListener[] = []
  private nextId = 1

  subscribe(listener: ToastListener) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  private notify() {
    this.listeners.forEach(listener => listener([...this.toasts]))
  }

  private generateId(): string {
    return `toast-${this.nextId++}-${Date.now()}`
  }

  show(toast: Omit<ToastData, 'id'>): string {
    const id = this.generateId()
    const newToast: ToastData = {
      id,
      duration: 4000,
      ...toast
    }

    this.toasts.push(newToast)
    this.notify()
    return id
  }

  success(title: string, message?: string, options?: Partial<ToastData>): string {
    return this.show({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  error(title: string, message?: string, options?: Partial<ToastData>): string {
    return this.show({
      type: 'error',
      title,
      message,
      duration: 6000, // Longer duration for errors
      ...options
    })
  }

  warning(title: string, message?: string, options?: Partial<ToastData>): string {
    return this.show({
      type: 'warning',
      title,
      message,
      ...options
    })
  }

  info(title: string, message?: string, options?: Partial<ToastData>): string {
    return this.show({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  close(id: string) {
    this.toasts = this.toasts.filter(toast => toast.id !== id)
    this.notify()
  }

  closeAll() {
    this.toasts = []
    this.notify()
  }

  // Convenience methods for common use cases
  contextCreated(contextName: string, vaultType: string) {
    return this.success(
      'Context Created Successfully',
      `"${contextName}" has been added to your ${vaultType} vault`,
      {
        action: {
          label: 'View Context',
          onClick: () => {
            // Navigate to files page with new context
            window.location.hash = '/files'
          }
        }
      }
    )
  }

  contextCreationFailed(error: string) {
    return this.error(
      'Failed to Create Context',
      error,
      {
        action: {
          label: 'Try Again',
          onClick: () => {
            // Could trigger retry logic
            console.log('Retry context creation')
          }
        }
      }
    )
  }

  vaultSyncCompleted(contextCount: number) {
    return this.success(
      'Vault Synchronized',
      `Found ${contextCount} context${contextCount !== 1 ? 's' : ''} in your vaults`
    )
  }
}

export const toastService = new ToastService()
