{"name": "chatlo-model-updater", "version": "1.0.0", "description": "Model update system for ChatLo - crawls OpenRouter and generates model manifests", "main": "modelCrawler.js", "scripts": {"crawl": "node modelCrawler.js", "test": "node test.js", "validate": "node validator.js"}, "dependencies": {"node-fetch": "^3.3.2"}, "devDependencies": {}, "keywords": ["chatlo", "openrouter", "models", "ai", "llm"], "author": "ChatLo Team", "license": "MIT"}