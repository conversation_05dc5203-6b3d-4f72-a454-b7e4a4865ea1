import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAppStore } from '../store'
import Chat<PERSON>o<PERSON>ogo from './ChatLoLogo'

interface MobileNavBarProps {
  className?: string
}

const MobileNavBar: React.FC<MobileNavBarProps> = ({ className = '' }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { sidebarOpen, setSidebarOpen, activeIconBarItem, setActiveIconBarItem } = useAppStore()

  const navigationItems = [
    {
      id: 'chat',
      icon: 'fa-solid fa-comment',
      label: 'Chat',
      path: '/',
      isActive: location.pathname === '/'
    },
    {
      id: 'history',
      icon: 'fa-solid fa-clock-rotate-left',
      label: 'History',
      path: '/history',
      isActive: location.pathname === '/history'
    },
    {
      id: 'files',
      icon: 'fa-solid fa-folder-tree',
      label: 'Files',
      path: '/files',
      isActive: location.pathname.startsWith('/files')
    },
    {
      id: 'settings',
      icon: 'fa-solid fa-gear',
      label: 'Settings',
      path: '/settings',
      isActive: location.pathname === '/settings'
    }
  ]

  const handleNavigation = (path: string, itemId: string) => {
    setActiveIconBarItem(itemId)
    navigate(path)
  }

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <div className={`md:hidden bg-gray-800 border-b border-tertiary ${className}`}>
      {/* Top bar with logo and menu */}
      <div className="flex items-center justify-between px-4 py-3">
        <ChatLoLogo variant="full" size="sm" />
        <button
          onClick={handleToggleSidebar}
          className="u1-button-ghost p-2"
          title="Toggle Sidebar"
        >
          <i className="fa-solid fa-bars"></i>
        </button>
      </div>

      {/* Bottom navigation */}
      <div className="flex items-center justify-around py-2 border-t border-tertiary/50">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => handleNavigation(item.path, item.id)}
            className={`
              flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors
              ${activeIconBarItem === item.id 
                ? 'text-primary bg-primary/10' 
                : 'text-gray-400 hover:text-supplement1'
              }
            `}
          >
            <i className={`${item.icon} text-sm`}></i>
            <span className="text-xs font-medium">{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

export default MobileNavBar
