# Files UI Layout Documentation

## Overview
The Files UI implements a comprehensive file management interface based on the file-system context vault philosophy. It provides a three-panel layout optimized for browsing, previewing, and interacting with context files while maintaining seamless integration with the chat system.

## Layout Structure

### Explorer Mode (Default)
```
┌─────────────────────────────────────────────────────────────────┐
│ Window Top Bar (h-6)                                           │
├─────────────────────────────────────────────────────────────────┤
│ Top Navigation Bar (h-12)                                      │
├─────┬───────────────────────────────────────────────────────────┤
│ Icon│ Main Files Content (flex-1)                              │
│ Bar │ ┌─────────────┬─────────────────────────────────────────┐ │
│(w-12│ │ File Tree   │ Right Panel (flex-1)                   │ │
│     │ │ Panel       │ ┌─────────────────────────────────────┐ │ │
│     │ │ (w-1/5)     │ │ Markdown Preview (h-[60%])          │ │ │
│     │ │             │ │                                     │ │ │
│     │ │             │ ├─────────────────────────────────────┤ │ │
│     │ │             │ │ Recent Chats (h-[40%])              │ │ │
│     │ │             │ │                                     │ │ │
│     │ │             │ └─────────────────────────────────────┘ │ │
│     │ └─────────────┴─────────────────────────────────────────┘ │
└─────┴───────────────────────────────────────────────────────────┘
```

### Master Mode (with Artifacts)
```
┌─────────────────────────────────────────────────────────────────┐
│ Window Top Bar (h-6)                                           │
├─────────────────────────────────────────────────────────────────┤
│ Top Navigation Bar (h-12)                                      │
├─────┬───────────────────────────────────────────────────────────┤
│ Icon│ Main Files Content (flex-1)                              │
│ Bar │ ┌─────────────┬─────────────────┬───────────────────────┐ │
│(w-12│ │ File Tree   │ Master.md       │ Artifacts Sidebar     │ │
│     │ │ Panel       │ Preview         │ (Standard Component)  │ │
│     │ │ (w-1/5)     │ (flex-1)        │ (w-80)               │ │
│     │ │             │                 │                       │ │
│     │ │             │                 │ - Document List       │ │
│     │ │             │                 │ - Copy/Download       │ │
│     │ │             │                 │ - Fullscreen Toggle   │ │
│     │ │             │                 │                       │ │
│     │ └─────────────┴─────────────────┴───────────────────────┘ │
└─────┴───────────────────────────────────────────────────────────┘
```

## Component Specifications

### 1. File Tree Panel (Left - 20% width)

#### Header Section
- **Title**: "Files in Vault" with supplement1 color
- **Add Button**: Plus icon with hover tooltip "Add File"
- **Border**: Bottom border with tertiary/50 color

#### View Toggle Buttons
- **Explorer Button**: Shows file tree view in vault folder
  - Icon: `fa-sitemap`
  - Text: "Explorer"
  - State: Active when browsing file tree
- **Master Button**: Opens master.md with Artifacts sidebar
  - Icon: `fa-lightbulb`
  - Text: "Master"
  - State: Active when viewing master.md with artifacts
  - Behavior: Triggers Artifacts sidebar component (reused from chat page)

#### File Tree Structure
```typescript
interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  isExpanded?: boolean
  isSelected?: boolean
  children?: FileTreeNode[]
  fileCount?: number
  icon: string
  color: string
}
```

#### Visual States
- **Hover**: `rgba(138, 176, 187, 0.1)` background
- **Selected**: `rgba(138, 176, 187, 0.2)` background + left border
- **Master.md**: Special highlighting with primary color and dot indicator

### 2. Content Display Area

#### Explorer Mode - Markdown Preview Section (Top Right - 60% height)
- **Content Area**: Full markdown rendering with custom CSS
- **Typography**: Inter font family with proper hierarchy
- **Colors**:
  - H1: `#D5D8E0` (supplement1)
  - H2: `#D5D8E0` (supplement1)
  - H3: `#89AFBA` (supplement2)
  - Body: `#9CA3AF` (gray-400)
  - Code: `#8AB0BB` (primary) on `#374151` background
- **AI Abstracts Panel**: Right sidebar (w-80) showing read-only AI summaries

#### Master Mode - Full Master.md Preview
- **Layout**: Full-width master.md content with Artifacts sidebar
- **Content**: Master document takes full available space (flex-1)
- **Artifacts Integration**: Standard Artifacts component slides in from right
- **Behavior**: Same Artifacts experience as chat page

### 3. Recent Chats Section (Bottom Right - 40% height)

#### Header
- **Title**: "Recent Chats" with supplement1 color
- **View All Link**: Primary colored link with hover effects

#### Chat Items
```typescript
interface ChatItem {
  id: string
  title: string
  preview: string
  timestamp: string
  messageCount: number
  isActive: boolean
}
```

#### Visual Design
- **Background**: `gray-700/50` with hover state `gray-700`
- **Layout**: Flex layout with title, preview, timestamp, and actions
- **Indicators**: Primary colored dot for active status
- **Actions**: Reply button with hover effects

## Color System Integration

### Primary Colors
- **Primary**: `#8AB0BB` - Used for active states, buttons, highlights
- **Secondary**: `#FF8383` - Used for accent elements, warnings
- **Tertiary**: `#1B3E68` - Used for borders, structural elements

### Supplement Colors  
- **Supplement1**: `#D5D8E0` - Used for primary text, headings
- **Supplement2**: `#89AFBA` - Used for secondary text, icons

### Background Colors
- **Main**: `gray-900` - Primary background
- **Panel**: `gray-800` - Secondary panels
- **Hover**: `gray-700` - Interactive hover states

## Responsive Behavior

### Breakpoints
- **Desktop**: Full three-panel layout
- **Tablet**: Collapsible file tree panel
- **Mobile**: Stack panels vertically with tabs

### Interaction Patterns
- **File Selection**: Updates preview and highlights tree item
- **Folder Toggle**: Expand/collapse with chevron rotation
- **Quick Actions**: Trigger appropriate chat or edit flows
- **Chat Integration**: Seamless transition to chat with file context

## State Management

### View Mode State
```typescript
interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}
```

### File Tree State
```typescript
interface FileTreeState {
  expandedFolders: Set<string>
  selectedFile: string | null
  fileTree: FileTreeNode[]
  isLoading: boolean
}
```

### Preview State
```typescript
interface PreviewState {
  currentFile: string | null
  content: string
  isLoading: boolean
  error: string | null
  masterDocPath: string | null
}
```

### Artifacts Integration State
```typescript
interface ArtifactsState {
  isVisible: boolean
  documents: ArtifactDocument[]
  selectedDocument: string | null
  isFullscreen: boolean
}
```

## Implementation Notes

### File System Integration
- Real-time file watching for updates
- Direct file system access for content
- Context folder scanning and indexing
- Master.md prioritization and highlighting

### Performance Optimizations
- Virtual scrolling for large file trees
- Lazy loading of file content
- Debounced search and filtering
- Cached file metadata

### Accessibility
- Keyboard navigation for file tree
- Screen reader support for all interactive elements
- Focus management for modal interactions
- High contrast mode support

This layout provides a comprehensive file management experience while maintaining the ChatLo design system consistency and supporting the file-based context vault philosophy outlined in the implementation plan.

## Development Implementation Guide

### Component Architecture
```
src/pages/FilesPage.tsx
├── FileTreePanel.tsx
│   ├── FileTreeHeader.tsx
│   ├── ViewToggleButtons.tsx
│   └── FileTreeView.tsx
├── ExplorerMode.tsx
│   ├── MarkdownPreviewSection.tsx
│   │   ├── MarkdownContent.tsx
│   │   └── AIAbstractsPanel.tsx
│   └── RecentChatsSection.tsx
│       ├── ChatHeader.tsx
│       └── ChatList.tsx
└── MasterMode.tsx
    ├── MasterDocumentPreview.tsx
    └── ArtifactsSidebar.tsx (shared component)
```

### Key Features from Design Reference

#### Navigation Breadcrumb
- **Location**: Top navigation bar center
- **Format**: "Project Alpha - Design System" with folder icon
- **Styling**: Tertiary background with supplement1 text
- **Navigation**: Left/right chevrons for context switching

#### File Tree Interactions
- **Folder Icons**: Supplement2 colored folder icons
- **File Icons**: Color-coded by type (primary for master.md, secondary for active files)
- **Counters**: Small circular badges showing file counts
- **Expansion**: Chevron icons that rotate on folder toggle

#### Master Document Focus
- **Special Treatment**: master.md files get primary color highlighting
- **Active Indicator**: Primary colored dot for currently selected file
- **Border**: Left border accent for selected items

#### Artifacts Integration
- **Standard Component**: Reuses existing Artifacts component from chat page
- **Slide-in Behavior**: Artifacts sidebar slides in when Master mode is activated
- **Document Management**: Same copy/download/fullscreen functionality as chat
- **Consistent Experience**: Identical user experience across Files and Chat pages

### Technical Requirements

#### File System Service Integration
```typescript
// Required service methods
interface FileSystemService {
  scanContextFolder(path: string): Promise<FileTreeNode[]>
  readFileContent(path: string): Promise<string>
  watchFileChanges(path: string, callback: (event: FileEvent) => void): void
  createFile(path: string, content: string): Promise<void>
  deleteFile(path: string): Promise<void>
}
```

#### Artifacts Service Integration
```typescript
// Artifacts integration methods
interface ArtifactsService {
  getArtifactsForContext(contextPath: string): Promise<ArtifactDocument[]>
  toggleArtifactsSidebar(visible: boolean): void
  expandArtifactFullscreen(documentId: string): void
  copyArtifactContent(documentId: string): Promise<void>
  downloadArtifact(documentId: string): Promise<void>
}
```

#### State Synchronization
- Real-time file system watching
- Automatic preview updates on file changes
- Context switching preserves selection state
- Chat history filtered by current context

This comprehensive layout documentation provides all necessary details for implementing the Files UI according to the design specifications and integration requirements.
