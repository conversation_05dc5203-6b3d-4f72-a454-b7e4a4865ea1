import React, { useState, useRef, useEffect } from 'react'
import { Artifact } from '../../../types'
import { ExternalLink, Globe, Copy, Check } from '../../Icons'

interface WebLinkArtifactViewerProps {
  artifact: Artifact
}

export function WebLinkArtifactViewer({ artifact }: WebLinkArtifactViewerProps) {
  const [viewMode, setViewMode] = useState<'card' | 'browser'>('card')
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const webviewRef = useRef<any>(null)

  const url = artifact.metadata.url || artifact.content
  const domain = artifact.metadata.domain || 'Unknown'
  const favicon = artifact.metadata.favicon

  // Generate preview image URL (using a service like urlbox.io or similar)
  const generatePreviewUrl = (targetUrl: string) => {
    // Using a free service for website screenshots
    return `https://api.urlbox.io/v1/ca482d7e-9417-4569-90fe-80f7c5e1c781/png?url=${encodeURIComponent(targetUrl)}&width=400&height=300&thumb_width=400`
  }

  useEffect(() => {
    // Load preview image
    const previewUrl = generatePreviewUrl(url)
    setPreviewImage(previewUrl)
  }, [url])

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy URL:', error)
    }
  }

  const handleOpenExternal = () => {
    if (window.electronAPI?.shell?.openExternal) {
      window.electronAPI.shell.openExternal(url)
    } else {
      window.open(url, '_blank')
    }
  }

  const handleViewInBrowser = () => {
    setViewMode('browser')
    setIsLoading(true)
  }

  const handleWebviewLoad = () => {
    setIsLoading(false)
  }

  if (viewMode === 'browser') {
    return (
      <div className="flex flex-col h-full bg-neutral-900">
        {/* Browser Header */}
        <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-b border-neutral-700">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('card')}
              className="px-3 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-neutral-200 rounded transition-colors"
            >
              ← Back to Card
            </button>
            <div className="flex items-center space-x-2 text-sm text-neutral-300">
              {favicon && <img src={favicon} alt="" className="w-4 h-4" />}
              <span className="truncate max-w-md">{url}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCopyUrl}
              className="p-2 text-neutral-400 hover:text-neutral-200 transition-colors"
              title="Copy URL"
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </button>
            <button
              onClick={handleOpenExternal}
              className="p-2 text-neutral-400 hover:text-neutral-200 transition-colors"
              title="Open in External Browser"
            >
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Browser Content */}
        <div className="flex-1 relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-neutral-900 z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto mb-4"></div>
                <div className="text-neutral-400">Loading website...</div>
              </div>
            </div>
          )}
          
          {/* Use webview for Electron or iframe for web */}
          {window.electronAPI ? (
            <webview
              ref={webviewRef}
              src={url}
              className="w-full h-full"
              onDomReady={handleWebviewLoad}
              allowpopups="true"
              useragent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            />
          ) : (
            <iframe
              src={url}
              className="w-full h-full border-0"
              onLoad={handleWebviewLoad}
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
            />
          )}
        </div>
      </div>
    )
  }

  // Card view
  return (
    <div className="flex flex-col h-full bg-neutral-900">
      {/* Header */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 bg-neutral-800 border-b border-neutral-700">
        <div className="flex items-center space-x-2">
          <Globe className="h-5 w-5 text-indigo-400" />
          <span className="text-sm font-medium text-neutral-200">Web Link</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopyUrl}
            className="p-2 text-neutral-400 hover:text-neutral-200 transition-colors"
            title="Copy URL"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
          </button>
          <button
            onClick={handleOpenExternal}
            className="p-2 text-neutral-400 hover:text-neutral-200 transition-colors"
            title="Open in External Browser"
          >
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Card Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div 
          className="bg-neutral-800 rounded-lg border border-neutral-700 overflow-hidden cursor-pointer hover:border-neutral-600 transition-colors"
          onClick={handleViewInBrowser}
        >
          {/* Preview Image */}
          <div className="aspect-video bg-neutral-700 relative overflow-hidden">
            {previewImage ? (
              <img
                src={previewImage}
                alt={`Preview of ${domain}`}
                className="w-full h-full object-cover"
                onError={() => setPreviewImage(null)}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-neutral-400">
                <div className="text-center">
                  <Globe className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <div className="text-sm">Website Preview</div>
                </div>
              </div>
            )}
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
              <div className="bg-black/60 text-white px-3 py-1 rounded text-sm">
                Click to view in browser
              </div>
            </div>
          </div>

          {/* Card Info */}
          <div className="p-4">
            <div className="flex items-center space-x-2 mb-2">
              {favicon && <img src={favicon} alt="" className="w-4 h-4" />}
              <h3 className="font-medium text-neutral-200 truncate">{domain}</h3>
            </div>
            
            <p className="text-sm text-neutral-400 truncate mb-3">
              {url}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-neutral-500">
                Click to open in viewer
              </span>
              <ExternalLink className="h-4 w-4 text-neutral-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          Web Link • {domain}
        </div>
        <div>
          {new Date(artifact.metadata.createdAt).toLocaleString()}
        </div>
      </div>
    </div>
  )
}
