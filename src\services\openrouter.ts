import { OpenRouterModel, ChatCompletionRequest } from '../types'

export class OpenRouterService {
  private apiKey: string | null = null
  private baseUrl = 'https://openrouter.ai/api/v1'

  setApiKey(apiKey: string) {
    this.apiKey = apiKey
  }

  async validateApiKey(): Promise<{ valid: boolean; error?: string }> {
    if (!this.apiKey) {
      return { valid: false, error: 'No API key provided' }
    }

    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        return { valid: true }
      } else {
        const errorData = await response.json().catch(() => ({}))
        return {
          valid: false,
          error: `API key validation failed (${response.status}): ${errorData.error?.message || response.statusText}`
        }
      }
    } catch (error) {
      return {
        valid: false,
        error: `Network error during API key validation: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  async getModels(): Promise<OpenRouterModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`)
      }

      const data = await response.json()
      const models = data.data || []

      // Debug: Log model count and check for latest models
      console.log(`OpenRouter API returned ${models.length} models`)
      const claudeModels = models.filter(m => m.id.includes('claude') || m.name.toLowerCase().includes('claude'))
      const gptModels = models.filter(m => m.id.includes('gpt') || m.name.toLowerCase().includes('gpt'))
      console.log('Claude models:', claudeModels.slice(0, 5).map(m => ({ id: m.id, name: m.name })))
      console.log('GPT models:', gptModels.slice(0, 5).map(m => ({ id: m.id, name: m.name })))

      return models
    } catch (error) {
      console.error('Error fetching models:', error)
      return []
    }
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not set')
    }

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatlo.app',
          'X-Title': 'Chatlo',
        },
        body: JSON.stringify({
          model: request.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.max_tokens || 4096,
          top_p: request.top_p,
          top_k: request.top_k,
          frequency_penalty: request.frequency_penalty,
          presence_penalty: request.presence_penalty,
          stop: request.stop,
          stream: false,
        }),
      })

      if (!response.ok) {
        let errorData: any = {}
        try {
          errorData = await response.json()
        } catch (e) {
          console.error('Failed to parse error response:', e)
        }

        console.error('OpenRouter API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          headers: Object.fromEntries(response.headers.entries())
        })

        let errorMessage = errorData.error?.message || errorData.message || 'Unknown error'

        // Add specific error messages for common status codes
        switch (response.status) {
          case 401:
            errorMessage = 'Invalid API key. Please check your OpenRouter API key in settings.'
            break
          case 403:
            errorMessage = 'Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.'
            break
          case 429:
            errorMessage = 'Rate limit exceeded. Please wait a moment before trying again.'
            break
          case 500:
            errorMessage = 'OpenRouter server error. Please try again later.'
            break
          case 502:
          case 503:
          case 504:
            errorMessage = 'OpenRouter service temporarily unavailable. Please try again later.'
            break
        }

        throw new Error(`OpenRouter API error (${response.status}): ${errorMessage}`)
      }

      const data = await response.json()
      return data.choices?.[0]?.message?.content || 'No response generated'
    } catch (error) {
      console.error('Error creating chat completion:', error)
      throw error
    }
  }

  async createStreamingChatCompletion(
    request: ChatCompletionRequest,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void> {
    if (!this.apiKey) {
      const error = new Error('OpenRouter API key not set')
      onError(error)
      return
    }

    // Validate required parameters
    if (!request.model) {
      const error = new Error('No model selected')
      onError(error)
      return
    }

    if (!request.messages || request.messages.length === 0) {
      const error = new Error('No messages provided')
      onError(error)
      return
    }

    console.log('Starting streaming chat completion with:', {
      model: request.model,
      messageCount: request.messages.length,
      temperature: request.temperature,
      maxTokens: request.max_tokens
    })

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatlo.app',
          'X-Title': 'Chatlo',
        },
        body: JSON.stringify({
          model: request.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.max_tokens || 4096,
          top_p: request.top_p,
          top_k: request.top_k,
          frequency_penalty: request.frequency_penalty,
          presence_penalty: request.presence_penalty,
          stop: request.stop,
          stream: true,
        }),
      })

      if (!response.ok) {
        let errorData: any = {}
        try {
          errorData = await response.json()
        } catch (e) {
          console.error('Failed to parse streaming error response:', e)
        }

        console.error('OpenRouter Streaming API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          headers: Object.fromEntries(response.headers.entries())
        })

        let errorMessage = errorData.error?.message || errorData.message || 'Unknown error'

        // Add specific error messages for common status codes
        switch (response.status) {
          case 401:
            errorMessage = 'Invalid API key. Please check your OpenRouter API key in settings.'
            break
          case 403:
            errorMessage = 'Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.'
            break
          case 429:
            errorMessage = 'Rate limit exceeded. Please wait a moment before trying again.'
            break
          case 500:
            errorMessage = 'OpenRouter server error. Please try again later.'
            break
          case 502:
          case 503:
          case 504:
            errorMessage = 'OpenRouter service temporarily unavailable. Please try again later.'
            break
        }

        throw new Error(`OpenRouter API error (${response.status}): ${errorMessage}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              onComplete()
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                onChunk(content)
              }
            } catch (e) {
              // Ignore parsing errors for individual chunks
            }
          }
        }
      }

      onComplete()
    } catch (error) {
      console.error('Error creating streaming chat completion:', error)
      onError(error as Error)
    }
  }
}

export const openRouterService = new OpenRouterService()
