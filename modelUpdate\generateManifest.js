/**
 * Generate models-manifest.json from OpenRouter API data
 * This is a simplified version that can be run in the browser environment
 */

// Sample of actual OpenRouter data (truncated for demo)
const sampleOpenRouterData = [
  {
    "id": "x-ai/grok-4",
    "name": "xAI: Grok 4",
    "description": "Grok 4 is xAI's latest reasoning model with a 256k context window. It supports parallel tool calling, structured outputs, and both image and text inputs.",
    "context_length": 256000,
    "architecture": {
      "modality": "text+image->text",
      "input_modalities": ["image", "text"],
      "output_modalities": ["text"],
      "tokenizer": "Grok",
      "instruct_type": null
    },
    "pricing": {
      "prompt": "0.000003",
      "completion": "0.000015"
    },
    "top_provider": {
      "context_length": 256000,
      "max_completion_tokens": null,
      "is_moderated": false
    },
    "created": **********
  },
  {
    "id": "google/gemini-2.5-pro",
    "name": "Google: Gemini 2.5 Pro",
    "description": "Gemini 2.5 Pro is Google's state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks.",
    "context_length": 1048576,
    "architecture": {
      "modality": "text+image->text",
      "input_modalities": ["file", "image", "text"],
      "output_modalities": ["text"],
      "tokenizer": "Gemini",
      "instruct_type": null
    },
    "pricing": {
      "prompt": "0.00000125",
      "completion": "0.00001"
    },
    "top_provider": {
      "context_length": 1048576,
      "max_completion_tokens": 65536,
      "is_moderated": false
    },
    "created": **********
  },
  {
    "id": "google/gemini-2.5-flash",
    "name": "Google: Gemini 2.5 Flash",
    "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks.",
    "context_length": 1048576,
    "architecture": {
      "modality": "text+image->text",
      "input_modalities": ["file", "image", "text"],
      "output_modalities": ["text"],
      "tokenizer": "Gemini",
      "instruct_type": null
    },
    "pricing": {
      "prompt": "0.0000003",
      "completion": "0.0000025"
    },
    "top_provider": {
      "context_length": 1048576,
      "max_completion_tokens": 65535,
      "is_moderated": false
    },
    "created": **********
  }
];

class ManifestGenerator {
  constructor() {
    this.flagshipCriteria = {
      'openai': ['gpt-4o', 'o1-preview', 'o1-mini', 'chatgpt-4o-latest'],
      'anthropic': ['claude-3.5-sonnet', 'claude-3-opus'],
      'google': ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-pro-1.5'],
      'x-ai': ['grok-4', 'grok-3'],
      'meta-llama': ['llama-3.3-70b', 'llama-3.1-405b', 'llama-3.1-70b'],
      'deepseek': ['deepseek-r1', 'deepseek-v3'],
      'qwen': ['qwen-2.5-72b', 'qwen-max']
    };
  }

  isFlagshipModel(model) {
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();
    
    for (const [provider, patterns] of Object.entries(this.flagshipCriteria)) {
      if (modelId.includes(provider)) {
        return patterns.some(pattern => 
          modelId.includes(pattern.toLowerCase()) || modelName.includes(pattern.toLowerCase())
        );
      }
    }
    return false;
  }

  categorizeModel(model) {
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();
    const description = (model.description || '').toLowerCase();
    
    const categories = [];
    
    // Free models
    if (model.pricing.prompt === "0" && model.pricing.completion === "0") {
      categories.push('free');
    }
    
    // Flagship models
    if (this.isFlagshipModel(model)) {
      categories.push('flagship');
    }
    
    // Reasoning models
    if (modelId.includes('o1-') || modelId.includes('reasoning') || 
        modelName.includes('thinking') || description.includes('reasoning')) {
      categories.push('reasoning');
    }
    
    // Vision models
    if (model.architecture.modality.includes('image') ||
        description.includes('vision') || description.includes('multimodal')) {
      categories.push('vision');
    }
    
    // Code models
    if (modelId.includes('code') || modelId.includes('devstral') ||
        description.includes('coding') || description.includes('software')) {
      categories.push('code');
    }
    
    return categories;
  }

  processModel(model) {
    return {
      id: model.id,
      name: model.name,
      description: model.description || '',
      context_length: model.context_length,
      pricing: {
        prompt: model.pricing.prompt,
        completion: model.pricing.completion
      },
      top_provider: {
        max_completion_tokens: model.top_provider.max_completion_tokens
      },
      architecture: {
        modality: model.architecture.modality,
        tokenizer: model.architecture.tokenizer,
        instruct_type: model.architecture.instruct_type
      },
      per_request_limits: model.per_request_limits,
      // Enhanced metadata
      provider: model.id.split('/')[0],
      categories: this.categorizeModel(model),
      is_flagship: this.isFlagshipModel(model),
      is_free: model.pricing.prompt === "0" && model.pricing.completion === "0",
      created_timestamp: model.created,
      last_updated: new Date().toISOString()
    };
  }

  generateManifest(rawModels) {
    const processedModels = rawModels.map(model => this.processModel(model));
    
    // Sort by flagship first, then by name
    processedModels.sort((a, b) => {
      if (a.is_flagship && !b.is_flagship) return -1;
      if (!a.is_flagship && b.is_flagship) return 1;
      return a.name.localeCompare(b.name);
    });
    
    const stats = {
      total_models: processedModels.length,
      flagship_models: processedModels.filter(m => m.is_flagship).length,
      free_models: processedModels.filter(m => m.is_free).length,
      vision_models: processedModels.filter(m => m.categories.includes('vision')).length,
      reasoning_models: processedModels.filter(m => m.categories.includes('reasoning')).length,
      code_models: processedModels.filter(m => m.categories.includes('code')).length,
      providers: [...new Set(processedModels.map(m => m.provider))].length
    };
    
    return {
      version: new Date().toISOString().split('T')[0].replace(/-/g, '.'),
      last_updated: new Date().toISOString(),
      crawl_timestamp: Date.now(),
      statistics: stats,
      models: processedModels,
      featured_models: processedModels
        .filter(m => m.is_flagship)
        .slice(0, 10)
        .map(m => m.id),
      deprecated_models: [],
      metadata: {
        source: 'OpenRouter API',
        generator_version: '1.0',
        note: 'Generated from sample data - replace with full API crawl'
      }
    };
  }
}

// Generate manifest from sample data
const generator = new ManifestGenerator();
const manifest = generator.generateManifest(sampleOpenRouterData);

// Output the manifest
console.log('Generated manifest:');
console.log(JSON.stringify(manifest, null, 2));

// For Node.js environment, save to file
if (typeof require !== 'undefined' && require.main === module) {
  const fs = require('fs');
  const path = require('path');
  
  const outputPath = path.join(__dirname, 'models-manifest.json');
  fs.writeFileSync(outputPath, JSON.stringify(manifest, null, 2), 'utf8');
  console.log(`Manifest saved to: ${outputPath}`);
}
