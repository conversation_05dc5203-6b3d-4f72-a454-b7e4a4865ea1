import React, { useState, useEffect, createContext, useContext } from 'react'

interface ModelUpdateSummary {
  total: number
  flagship: number
  free: number
  vision: number
  reasoning: number
  code: number
  search: number
}

interface ToastProps {
  message: string
  type: 'success' | 'error' | 'info'
  duration?: number
  onClose: () => void
  updateTime?: string
  summary?: ModelUpdateSummary
}

export function ArtifactToast({ message, type, duration = 3000, onClose, updateTime, summary }: ToastProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 300) // Wait for animation to complete
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  const getToastClasses = () => {
    const baseClasses = `
      fixed bottom-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg
      flex flex-col transition-all duration-300
      backdrop-blur-sm border ${summary ? 'max-w-md' : 'max-w-sm'}
    `
    
    const typeClasses = {
      success: 'bg-green-600/90 text-white border-green-500/50 shadow-green-500/25',
      error: 'bg-red-600/90 text-white border-red-500/50 shadow-red-500/25',
      info: 'bg-indigo-600/90 text-white border-indigo-500/50 shadow-indigo-500/25'
    }
    
    const visibilityClasses = isVisible 
      ? 'translate-y-0 opacity-100' 
      : 'translate-y-2 opacity-0'
    
    return `${baseClasses} ${typeClasses[type]} ${visibilityClasses}`
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case 'error':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
      case 'info':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  return (
    <div className={getToastClasses()}>
      {/* Header */}
      <div className="flex items-center space-x-2">
        {getIcon()}
        <span className="text-sm font-medium flex-1">{message}</span>
        <button
          onClick={() => {
            setIsVisible(false)
            setTimeout(onClose, 300)
          }}
          className="text-white/70 hover:text-white transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Update time */}
      {updateTime && (
        <div className="text-xs text-white/60 mt-1">
          {updateTime}
        </div>
      )}

      {/* Summary table */}
      {summary && (
        <div className="mt-3 pt-3 border-t border-white/20">
          <div className="text-xs text-white/80 mb-2 font-medium">Model Summary:</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-white/70">Total:</span>
              <span className="font-medium">{summary.total}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Flagship:</span>
              <span className="font-medium">{summary.flagship}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Free:</span>
              <span className="font-medium">{summary.free}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Vision:</span>
              <span className="font-medium">{summary.vision}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Reasoning:</span>
              <span className="font-medium">{summary.reasoning}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Code:</span>
              <span className="font-medium">{summary.code}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Search:</span>
              <span className="font-medium">{summary.search}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Toast manager hook
interface Toast {
  id: string
  message: string
  type: 'success' | 'error' | 'info'
  duration?: number
  updateTime?: string
  summary?: ModelUpdateSummary
}

// Toast Context
interface ToastContextType {
  success: (message: string, duration?: number, updateTime?: string, summary?: ModelUpdateSummary) => void
  error: (message: string, duration?: number) => void
  info: (message: string, duration?: number) => void
}

const ToastContext = createContext<ToastContextType | null>(null)

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = (message: string, type: 'success' | 'error' | 'info', duration?: number, updateTime?: string, summary?: ModelUpdateSummary) => {
    const id = Math.random().toString(36).substr(2, 9)
    const toast: Toast = { id, message, type, duration, updateTime, summary }

    setToasts(prev => [...prev, toast])
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const contextValue: ToastContextType = {
    success: (message: string, duration?: number, updateTime?: string, summary?: ModelUpdateSummary) =>
      addToast(message, 'success', duration, updateTime, summary),
    error: (message: string, duration?: number) => addToast(message, 'error', duration),
    info: (message: string, duration?: number) => addToast(message, 'info', duration)
  }

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      {/* Toast Container */}
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <ArtifactToast
            key={toast.id}
            message={toast.message}
            type={toast.type}
            duration={toast.duration}
            updateTime={toast.updateTime}
            summary={toast.summary}
            onClose={() => removeToast(toast.id)}
          />
        ))}
      </div>
    </ToastContext.Provider>
  )
}

export function useArtifactToasts() {
  const context = useContext(ToastContext)
  if (!context) {
    // Fallback for when context is not available
    return {
      success: (message: string) => console.log('Success:', message),
      error: (message: string) => console.error('Error:', message),
      info: (message: string) => console.info('Info:', message)
    }
  }
  return context
}
