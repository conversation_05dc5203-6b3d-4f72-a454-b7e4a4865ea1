import React, { useState, useEffect } from 'react'
import { DatabaseHealth } from '../types'

const DatabaseDiagnostics: React.FC = () => {
  const [health, setHealth] = useState<DatabaseHealth | null>(null)
  const [loading, setLoading] = useState(false)
  const [backupStatus, setBackupStatus] = useState<string>('')

  const checkHealth = async () => {
    setLoading(true)
    try {
      const healthData = await window.electronAPI.db.getDatabaseHealth()
      setHealth(healthData)
    } catch (error) {
      console.error('Failed to check database health:', error)
    } finally {
      setLoading(false)
    }
  }

  const createBackup = async () => {
    setLoading(true)
    setBackupStatus('Creating backup...')
    try {
      const backupPath = await window.electronAPI.db.createBackup()
      setBackupStatus(`Backup created: ${backupPath}`)
      // Refresh health data to update last backup time
      await checkHealth()
    } catch (error) {
      setBackupStatus(`Backup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkHealth()
  }, [])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Database Diagnostics</h1>
          <div className="flex gap-4">
            <button
              onClick={checkHealth}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Checking...' : 'Refresh'}
            </button>
            <button
              onClick={createBackup}
              disabled={loading}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              Create Backup
            </button>
          </div>
        </div>

        {backupStatus && (
          <div className={`mb-6 p-4 rounded-lg ${
            backupStatus.includes('failed') ? 'bg-red-900/50 border border-red-500' : 'bg-green-900/50 border border-green-500'
          }`}>
            {backupStatus}
          </div>
        )}

        {health && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Health Status */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${health.isHealthy ? 'bg-green-500' : 'bg-red-500'}`}></div>
                Database Health
              </h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className={health.isHealthy ? 'text-green-400' : 'text-red-400'}>
                    {health.isHealthy ? 'Healthy' : 'Issues Detected'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Integrity Check:</span>
                  <span className={health.integrityCheck === 'ok' ? 'text-green-400' : 'text-red-400'}>
                    {health.integrityCheck}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Database Version:</span>
                  <span>{health.version}</span>
                </div>
              </div>
            </div>

            {/* Database Statistics */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Statistics</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Tables:</span>
                  <span>{health.tableCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Records:</span>
                  <span>{health.totalRecords.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Database Size:</span>
                  <span>{formatFileSize(health.fileSize)}</span>
                </div>
                {health.lastBackup && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Backup:</span>
                    <span className="text-sm">{formatDate(health.lastBackup)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Recommendations */}
            <div className="bg-gray-800 rounded-lg p-6 md:col-span-2">
              <h2 className="text-xl font-semibold mb-4">Recommendations</h2>
              <div className="space-y-2">
                {!health.isHealthy && (
                  <div className="flex items-start gap-2 text-red-400">
                    <span className="text-red-500 mt-1">⚠️</span>
                    <span>Database integrity issues detected. Consider creating a backup and restarting the application.</span>
                  </div>
                )}
                {health.fileSize > 100 * 1024 * 1024 && (
                  <div className="flex items-start gap-2 text-yellow-400">
                    <span className="text-yellow-500 mt-1">💡</span>
                    <span>Database is large ({formatFileSize(health.fileSize)}). Consider archiving old conversations.</span>
                  </div>
                )}
                {!health.lastBackup && (
                  <div className="flex items-start gap-2 text-blue-400">
                    <span className="text-blue-500 mt-1">💡</span>
                    <span>No recent backup found. Consider creating a backup regularly.</span>
                  </div>
                )}
                {health.isHealthy && health.lastBackup && health.fileSize < 50 * 1024 * 1024 && (
                  <div className="flex items-start gap-2 text-green-400">
                    <span className="text-green-500 mt-1">✅</span>
                    <span>Database is healthy and well-maintained.</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {loading && !health && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-3">Checking database health...</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default DatabaseDiagnostics
