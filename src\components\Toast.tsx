import React, { useEffect, useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faCheckCircle, 
  faExclamationTriangle, 
  faInfoCircle, 
  faTimes 
} from '@fortawesome/free-solid-svg-icons'

export interface ToastData {
  id: string
  type: 'success' | 'error' | 'info' | 'warning'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface ToastProps {
  toast: ToastData
  onClose: (id: string) => void
}

export const Toast: React.FC<ToastProps> = ({ toast, onClose }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Animate in
    setTimeout(() => setIsVisible(true), 10)

    // Auto-close after duration
    if (toast.duration !== 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, toast.duration || 4000)

      return () => clearTimeout(timer)
    }
  }, [toast.duration])

  const handleClose = () => {
    setIsExiting(true)
    setTimeout(() => {
      onClose(toast.id)
    }, 300)
  }

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return faCheckCircle
      case 'error':
        return faExclamationTriangle
      case 'warning':
        return faExclamationTriangle
      case 'info':
      default:
        return faInfoCircle
    }
  }

  const getColors = () => {
    switch (toast.type) {
      case 'success':
        return {
          bg: 'bg-green-900/90',
          border: 'border-green-500/50',
          icon: 'text-green-400',
          text: 'text-green-100'
        }
      case 'error':
        return {
          bg: 'bg-red-900/90',
          border: 'border-red-500/50',
          icon: 'text-red-400',
          text: 'text-red-100'
        }
      case 'warning':
        return {
          bg: 'bg-yellow-900/90',
          border: 'border-yellow-500/50',
          icon: 'text-yellow-400',
          text: 'text-yellow-100'
        }
      case 'info':
      default:
        return {
          bg: 'bg-blue-900/90',
          border: 'border-blue-500/50',
          icon: 'text-blue-400',
          text: 'text-blue-100'
        }
    }
  }

  const colors = getColors()

  return (
    <div
      className={`
        fixed right-4 z-50 w-96 max-w-[90vw] rounded-lg border backdrop-blur-sm shadow-lg
        transition-all duration-300 ease-out
        ${colors.bg} ${colors.border}
        ${isVisible && !isExiting 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
        }
      `}
      style={{
        top: `${80 + (parseInt(toast.id.split('-')[1]) || 0) * 110}px`
      }}
    >
      <div className="p-4">
        <div className="flex items-start gap-3">
          <FontAwesomeIcon 
            icon={getIcon()} 
            className={`${colors.icon} text-lg mt-0.5 flex-shrink-0`} 
          />
          
          <div className="flex-1 min-w-0">
            <h4 className={`font-medium ${colors.text} text-sm`}>
              {toast.title}
            </h4>
            {toast.message && (
              <p className={`${colors.text} text-xs mt-1 opacity-90`}>
                {toast.message}
              </p>
            )}
            {toast.action && (
              <button
                onClick={toast.action.onClick}
                className={`mt-2 text-xs ${colors.icon} hover:underline font-medium`}
              >
                {toast.action.label}
              </button>
            )}
          </div>
          
          <button
            onClick={handleClose}
            className={`${colors.text} hover:opacity-70 transition-opacity flex-shrink-0`}
          >
            <FontAwesomeIcon icon={faTimes} className="text-sm" />
          </button>
        </div>
      </div>
    </div>
  )
}

// Toast Container Component
interface ToastContainerProps {
  toasts: ToastData[]
  onClose: (id: string) => void
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onClose }) => {
  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {toasts.map((toast) => (
        <div key={toast.id} className="pointer-events-auto">
          <Toast toast={toast} onClose={onClose} />
        </div>
      ))}
    </div>
  )
}
