<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Link Artifact Detection Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .container {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .artifact {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #4f46e5;
        }
        .url {
            color: #60a5fa;
            word-break: break-all;
        }
        .domain {
            color: #34d399;
            font-weight: bold;
        }
        .processed {
            background: #1f2937;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        h1, h2 {
            color: #f3f4f6;
        }
        .stats {
            background: #065f46;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔗 Web Link Artifact Detection Test</h1>
    
    <div class="container">
        <h2>Test Message:</h2>
        <div id="testMessage" class="processed"></div>
    </div>

    <div class="container">
        <h2>Detection Results:</h2>
        <div id="results"></div>
    </div>

    <div class="container">
        <h2>Processed Content:</h2>
        <div id="processedContent" class="processed"></div>
    </div>

    <script>
        // Simulate the artifact detection logic
        function detectWebLinks(content, messageId) {
            const urlRegex = /https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)/g;
            const artifacts = [];
            let processedContent = content;
            let match;
            let index = 0;

            while ((match = urlRegex.exec(content)) !== null) {
                const url = match[0];
                let domain;
                try {
                    domain = new URL(url).hostname.replace('www.', '');
                } catch {
                    domain = 'Unknown';
                }

                const artifact = {
                    id: `artifact-${messageId}-weblink-${index}`,
                    type: 'weblink',
                    title: `Website: ${domain}`,
                    content: url,
                    metadata: {
                        url,
                        domain,
                        favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
                        createdAt: new Date().toISOString(),
                        messageId,
                        originalIndex: index
                    },
                    isActive: false
                };

                artifacts.push(artifact);
                
                // Replace with placeholder
                const placeholder = `[🌐 View ${artifact.title}]`;
                processedContent = processedContent.replace(url, placeholder);
                
                index++;
            }

            return { artifacts, processedContent };
        }

        // Test message
        const testMessage = `Here are some useful resources:

1. OpenAI's website: https://openai.com
2. GitHub repository: https://github.com/microsoft/vscode
3. Documentation: https://docs.anthropic.com/claude/docs
4. News article: https://www.bbc.com/news/technology-12345678
5. Research paper: https://arxiv.org/abs/2301.00001

You can also check out:
- https://stackoverflow.com/questions/12345/how-to-code
- https://www.youtube.com/watch?v=dQw4w9WgXcQ
- https://medium.com/@author/article-title-123abc

Let me know if you need more information!`;

        // Run the test
        const result = detectWebLinks(testMessage, 'test-message-123');
        
        // Display test message
        document.getElementById('testMessage').textContent = testMessage;
        
        // Display results
        const resultsDiv = document.getElementById('results');
        const webLinkArtifacts = result.artifacts.filter(a => a.type === 'weblink');
        
        resultsDiv.innerHTML = `
            <div class="stats">
                📊 Total artifacts found: ${result.artifacts.length}<br>
                🌐 Web link artifacts: ${webLinkArtifacts.length}
            </div>
        `;
        
        webLinkArtifacts.forEach((artifact, index) => {
            const artifactDiv = document.createElement('div');
            artifactDiv.className = 'artifact';
            artifactDiv.innerHTML = `
                <strong>${index + 1}. ${artifact.title}</strong><br>
                <span class="url">URL: ${artifact.content}</span><br>
                <span class="domain">Domain: ${artifact.metadata.domain}</span><br>
                <small>Favicon: ${artifact.metadata.favicon}</small>
            `;
            resultsDiv.appendChild(artifactDiv);
        });
        
        // Display processed content
        document.getElementById('processedContent').textContent = result.processedContent;
        
        console.log('🔗 Web Link Detection Test Results:', result);
    </script>
</body>
</html>
