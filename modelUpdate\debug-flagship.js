/**
 * Debug Flagship Detection
 * Test the flagship detection logic for Claude 4 models
 */

const ModelCrawler = require('./modelCrawler.js');

// Test models
const testModels = [
  {
    id: "anthropic/claude-opus-4",
    name: "Anthropic: Claude Opus 4",
    description: "Claude Opus 4 is benchmarked as the world's best coding model...",
    pricing: { prompt: "0.000015", completion: "0.000075" }
  },
  {
    id: "anthropic/claude-sonnet-4", 
    name: "Anthropic: Claude Sonnet 4",
    description: "Claude Sonnet 4 significantly enhances the capabilities...",
    pricing: { prompt: "0.000003", completion: "0.000015" }
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Anthropic: Claude 3.5 Sonnet", 
    description: "Claude 3.5 Sonnet delivers better-than-Opus capabilities...",
    pricing: { prompt: "0.000003", completion: "0.000015" }
  },
  {
    id: "anthropic/claude-3-opus",
    name: "Anthropic: Claude 3 Opus",
    description: "Claude 3 Opus is Anthropic's most powerful model...",
    pricing: { prompt: "0.000015", completion: "0.000075" }
  }
];

function debugFlagshipDetection() {
  console.log('🔍 Debugging Flagship Detection for Claude Models (Updated Logic)\n');

  const crawler = new ModelCrawler();

  testModels.forEach(model => {
    console.log(`🧪 Testing: ${model.name}`);
    console.log(`   ID: ${model.id}`);

    const isFlagship = crawler.isFlagshipModel(model);
    console.log(`   Is Flagship: ${isFlagship ? '✅ YES' : '❌ NO'}`);

    // Debug the new logic
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();

    console.log(`   Model ID (lower): ${modelId}`);
    console.log(`   Model Name (lower): ${modelName}`);

    // Check if it's Anthropic Claude
    const isAnthropicClaude = modelId.includes('anthropic') && modelId.includes('claude');
    console.log(`   Is Anthropic Claude: ${isAnthropicClaude ? '✅' : '❌'}`);

    if (isAnthropicClaude) {
      // Check specific Claude 4 patterns
      const isClaude4 = modelId.includes('claude-opus-4') || modelId.includes('claude-sonnet-4');
      console.log(`   Is Claude 4: ${isClaude4 ? '✅' : '❌'}`);

      // Check Claude 3.5/3 patterns
      const isClaude35Sonnet = modelId.includes('claude-3.5-sonnet');
      const isClaude3Opus = modelId.includes('claude-3-opus');
      console.log(`   Is Claude 3.5 Sonnet: ${isClaude35Sonnet ? '✅' : '❌'}`);
      console.log(`   Is Claude 3 Opus: ${isClaude3Opus ? '✅' : '❌'}`);

      // Check exclusions
      const isExcluded = modelId.includes('claude-2') || modelId.includes('claude-instant') ||
                        modelId.includes('claude-3-haiku');
      console.log(`   Is Excluded: ${isExcluded ? '❌ YES' : '✅ NO'}`);

      const shouldBeFlagship = (isClaude4 || isClaude35Sonnet || isClaude3Opus) && !isExcluded;
      console.log(`   Should Be Flagship: ${shouldBeFlagship ? '✅ YES' : '❌ NO'}`);
    }

    console.log('');
  });
}

debugFlagshipDetection();
