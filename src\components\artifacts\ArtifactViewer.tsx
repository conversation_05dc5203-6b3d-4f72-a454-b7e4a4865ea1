import React from 'react'
import { Artifact } from '../../types'
import { ImageArtifactViewer } from './viewers/ImageArtifactViewer'
import { CodeArtifactViewer } from './viewers/CodeArtifactViewer'
import { MarkdownArtifactViewer } from './viewers/MarkdownArtifactViewer'
import { MermaidArtifactViewer } from './viewers/MermaidArtifactViewer'
import { HtmlArtifactViewer } from './viewers/HtmlArtifactViewer'
import { WebLinkArtifactViewer } from './viewers/WebLinkArtifactViewer'

interface ArtifactViewerProps {
  artifact: Artifact
}

export function ArtifactViewer({ artifact }: ArtifactViewerProps) {
  const [isLoading, setIsLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  const handleCopyRendered = async () => {
    try {
      // For images, copy the image data; for text-based artifacts, copy the content
      if (artifact.type === 'image') {
        // Copy image to clipboard (if supported)
        if (navigator.clipboard && window.ClipboardItem) {
          const response = await fetch(artifact.content)
          const blob = await response.blob()
          await navigator.clipboard.write([
            new ClipboardItem({ [blob.type]: blob })
          ])
        } else {
          // Fallback: copy image URL
          await navigator.clipboard.writeText(artifact.content)
        }
      } else {
        // Copy rendered content (same as source for most types)
        await navigator.clipboard.writeText(artifact.content)
      }
      console.log('Copied rendered content to clipboard')
    } catch (err) {
      console.error('Failed to copy rendered content:', err)
    }
  }

  const handleCopySource = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      console.log('Copied source to clipboard')
    } catch (err) {
      console.error('Failed to copy source:', err)
    }
  }

  const handleDownload = () => {
    try {
      if (artifact.type === 'image') {
        // Download image
        const link = document.createElement('a')
        link.href = artifact.content
        link.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        // Download text-based content
        const blob = new Blob([artifact.content], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }
      console.log('Downloaded artifact')
    } catch (err) {
      console.error('Failed to download artifact:', err)
    }
  }

  React.useEffect(() => {
    // Simulate loading for complex artifacts
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 100)

    return () => clearTimeout(timer)
  }, [artifact.id])

  const renderArtifact = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-neutral-400">
            <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            <div>Loading artifact...</div>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full text-red-400">
          <div className="text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <div className="text-lg font-medium mb-2">Error Loading Artifact</div>
            <div className="text-sm">{error}</div>
          </div>
        </div>
      )
    }
    switch (artifact.type) {
      case 'image':
        return <ImageArtifactViewer artifact={artifact} />
      
      case 'code':
      case 'json':
        return <CodeArtifactViewer artifact={artifact} />
      
      case 'markdown':
        return <MarkdownArtifactViewer artifact={artifact} />
      
      case 'mermaid':
        return <MermaidArtifactViewer artifact={artifact} />
      
      case 'html':
        return <HtmlArtifactViewer artifact={artifact} />

      case 'weblink':
        return <WebLinkArtifactViewer artifact={artifact} />

      default:
        return (
          <div className="flex items-center justify-center h-full text-neutral-400">
            <div className="text-center">
              <div className="text-4xl mb-4">❓</div>
              <div className="text-lg font-medium mb-2">Unsupported Artifact Type</div>
              <div className="text-sm">
                Type: {artifact.type}
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Artifact actions - only essential icons */}
      <div className="flex items-center bg-gray-800/50 border-b border-tertiary relative">
        <div className="flex gap-1 p-2">
          {/* Copy Rendered */}
          <button
            onClick={handleCopyRendered}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors group relative"
            title="Copy Rendered"
          >
            <i className="fa-solid fa-copy text-gray-400 text-xs"></i>
          </button>

          {/* Copy Source */}
          <button
            onClick={handleCopySource}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors group relative"
            title="Copy Source"
          >
            <i className="fa-solid fa-code text-gray-400 text-xs"></i>
          </button>

          {/* Download */}
          <button
            onClick={handleDownload}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors group relative"
            title="Download"
          >
            <i className="fa-solid fa-download text-gray-400 text-xs"></i>
          </button>
        </div>
      </div>

      {/* Artifact content */}
      <div className="flex-1 overflow-hidden">
        {renderArtifact()}
      </div>
    </div>
  )
}

// Helper functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}
