<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Files Explorer View UI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: "#8AB0BB",
                        secondary: "#FF8383",
                        tertiary: "#1B3E68",
                        supplement1: "#D5D8E0",
                        supplement2: "#89AFBA"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', sans-serif; }
        .file-item:hover { background-color: rgba(138, 176, 187, 0.1); }
        .file-item.selected { background-color: rgba(138, 176, 187, 0.2); border-left: 2px solid #8AB0BB; }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <!-- Explorer View Layout -->
    <div class="h-screen flex bg-gray-900">

        <!-- Left Panel - File Tree (20%) -->
        <div class="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">

            <!-- Header -->
            <div class="p-4 border-b border-tertiary/50">
                <div class="flex items-center justify-between">
                    <h3 class="font-medium text-supplement1 text-sm">Files in Vault</h3>
                    <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                        <i class="fa-plus text-gray-400 text-xs"></i>
                    </button>
                </div>
            </div>

            <!-- View Toggle -->
            <div class="p-3 border-b border-tertiary/50">
                <div class="flex gap-2">
                    <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-secondary text-gray-900 hover:bg-secondary/80 transition-colors">
                        <i class="fa-sitemap text-sm"></i>
                        <span class="text-xs font-medium">Explorer</span>
                    </button>
                    <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 text-supplement1 transition-colors">
                        <i class="fa-lightbulb text-sm"></i>
                        <span class="text-xs font-medium">Master</span>
                    </button>
                </div>
            </div>

            <!-- File Tree -->
            <div class="flex-1 overflow-y-auto p-2">
                <!-- Root folder -->
                <div class="file-item p-2 rounded cursor-pointer flex items-center gap-2">
                    <i class="fa-chevron-down text-gray-400 text-xs w-3"></i>
                    <i class="fa-folder text-supplement2 text-sm"></i>
                    <span class="text-sm text-supplement1">project-alpha</span>
                    <div class="ml-auto">
                        <span class="w-5 h-5 bg-secondary/20 text-secondary text-xs rounded-full flex items-center justify-center font-medium">8</span>
                    </div>
                </div>

                <!-- Files and folders -->
                <div class="ml-4">
                    <div class="file-item p-2 rounded cursor-pointer flex items-center gap-2">
                        <div class="w-3"></div>
                        <i class="fa-file-lines text-primary text-sm"></i>
                        <span class="text-sm text-primary font-medium">master.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                        </div>
                    </div>

                    <div class="file-item p-2 rounded cursor-pointer flex items-center gap-2">
                        <i class="fa-chevron-right text-gray-400 text-xs w-3"></i>
                        <i class="fa-folder text-supplement2 text-sm"></i>
                        <span class="text-sm text-supplement1">documents</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">3</span>
                        </div>
                    </div>

                    <div class="file-item p-2 rounded cursor-pointer flex items-center gap-2">
                        <i class="fa-chevron-right text-gray-400 text-xs w-3"></i>
                        <i class="fa-folder text-supplement2 text-sm"></i>
                        <span class="text-sm text-supplement1">images</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">5</span>
                        </div>
                    </div>

                    <div class="file-item p-2 rounded cursor-pointer flex items-center gap-2">
                        <div class="w-3"></div>
                        <i class="fa-file-code text-supplement2 text-sm"></i>
                        <span class="text-sm text-supplement1">config.json</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - File Explorer Grid (80%) -->
        <div class="flex-1 flex flex-col bg-gray-900">

            <!-- Breadcrumb Navigation -->
            <div class="p-4 border-b border-tertiary/50 bg-gray-800/50">
                <div class="flex items-center gap-2">
                    <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                        <i class="fa-chevron-left text-gray-400 text-sm"></i>
                    </button>
                    <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                        <i class="fa-chevron-right text-gray-400 text-sm"></i>
                    </button>
                    <div class="flex items-center gap-1 ml-2">
                        <i class="fa-folder text-supplement2 text-sm"></i>
                        <span class="text-sm text-supplement1">project-alpha</span>
                        <i class="fa-chevron-right text-gray-400 text-xs"></i>
                        <span class="text-sm text-gray-400">documents</span>
                    </div>
                    <div class="ml-auto flex items-center gap-2">
                        <button class="p-2 hover:bg-gray-700 rounded transition-colors">
                            <i class="fa-th text-gray-400 text-sm"></i>
                        </button>
                        <button class="p-2 hover:bg-gray-700 rounded transition-colors">
                            <i class="fa-list text-primary text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- File Grid/List View -->
            <div class="flex-1 overflow-y-auto p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">

                    <!-- File Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-file-pdf text-primary text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">design-spec.pdf</h4>
                            <p class="text-xs text-gray-400">2.4 MB • PDF</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 2h ago</p>
                        </div>
                    </div>

                    <!-- File Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-file-word text-secondary text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">requirements.docx</h4>
                            <p class="text-xs text-gray-400">1.8 MB • Word</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 1d ago</p>
                        </div>
                    </div>

                    <!-- File Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-supplement2/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-file-lines text-supplement2 text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">notes.md</h4>
                            <p class="text-xs text-gray-400">12 KB • Markdown</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 3h ago</p>
                        </div>
                    </div>

                    <!-- File Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-file-excel text-green-400 text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">data-analysis.xlsx</h4>
                            <p class="text-xs text-gray-400">856 KB • Excel</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 5h ago</p>
                        </div>
                    </div>

                    <!-- Folder Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-supplement2/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-folder text-supplement2 text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">assets</h4>
                            <p class="text-xs text-gray-400">12 items</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 1d ago</p>
                        </div>
                    </div>

                    <!-- Image Item -->
                    <div class="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-3">
                                <i class="fa-file-image text-purple-400 text-xl"></i>
                            </div>
                            <h4 class="text-sm font-medium text-supplement1 mb-1 truncate w-full">mockup.png</h4>
                            <p class="text-xs text-gray-400">3.2 MB • PNG</p>
                            <p class="text-xs text-gray-500 mt-1">Modified 6h ago</p>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Status Bar -->
            <div class="p-3 border-t border-tertiary/50 bg-gray-800/50">
                <div class="flex items-center justify-between text-xs text-gray-400">
                    <span>6 items selected</span>
                    <span>Total: 24 files, 8.4 MB</span>
                </div>
            </div>
        </div>
    </div>

</body>
</html>