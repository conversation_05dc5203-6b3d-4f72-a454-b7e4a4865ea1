import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  base: './',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          'react-router': ['react-router-dom'],
          'markdown': ['react-markdown', 'remark-gfm'],
          'file-processing': ['mammoth', 'pdf-parse', 'tesseract.js', 'xlsx', 'sharp', 'jszip'],
          'utilities': ['uuid', 'mime-types', 'chokidar'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
    target: 'es2020',
    sourcemap: false,
  },
  server: {
    port: 5173,
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
})
