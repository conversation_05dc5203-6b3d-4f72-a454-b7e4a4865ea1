import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { Plus, Settings as SettingsIcon, MessageSquare, Trash2, Edit3, CheckCircle, AlertCircle, Search, Pin, MoreHorizontal, History } from './Icons'
import ConversationItem from './ConversationItem'
import { useNavigate } from 'react-router-dom'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './ChatLoLogo'
import NetworkStatus from './NetworkStatus'

// Toast notification component
const Toast: React.FC<{ message: string; type: 'success' | 'error'; onClose: () => void }> = ({ message, type, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 4000)
    return () => clearTimeout(timer)
  }, [onClose])

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg backdrop-blur-lg border ${
      type === 'success'
        ? 'bg-green-900/80 border-green-700 text-green-100'
        : 'bg-red-900/80 border-red-700 text-red-100'
    }`}>
      <div className="flex items-center gap-2">
        {type === 'success' ? (
          <CheckCircle className="h-5 w-5" />
        ) : (
          <AlertCircle className="h-5 w-5" />
        )}
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={onClose}
          className="ml-2 text-current hover:opacity-70"
        >
          ×
        </button>
      </div>
    </div>
  )
}

const Sidebar: React.FC = () => {
  const navigate = useNavigate()
  const {
    conversations,
    currentConversationId,
    setCurrentConversation,
    createConversation,
    createDraftConversation,
    deleteConversation,
    loadMessages,
    searchTerm,
    setSearchTerm,
    conversationFilter,
    setConversationFilter,
    loadConversationsWithFilter,
    searchConversations,
  } = useAppStore()

  // Use conversations from store (already filtered by search)
  const filteredConversations = conversations

  const pinnedConversations = filteredConversations.filter(c => c.is_pinned === 1)
  const unpinnedConversations = filteredConversations.filter(c => c.is_pinned === 0)

  // Group conversations by date
  const groupConversationsByDate = (conversations: typeof unpinnedConversations) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

    const groups = {
      today: [] as typeof conversations,
      yesterday: [] as typeof conversations,
      lastWeek: [] as typeof conversations,
      older: [] as typeof conversations
    }

    conversations.forEach(conv => {
      const convDate = new Date(conv.updated_at || conv.created_at)
      if (convDate >= today) {
        groups.today.push(conv)
      } else if (convDate >= yesterday) {
        groups.yesterday.push(conv)
      } else if (convDate >= lastWeek) {
        groups.lastWeek.push(conv)
      } else {
        groups.older.push(conv)
      }
    })

    return groups
  }

  const conversationGroups = groupConversationsByDate(unpinnedConversations)
  
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState('')
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'checking' | 'available' | 'downloading' | 'downloaded' | 'error'>('idle')
  const [updateError, setUpdateError] = useState<string | null>(null)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null)

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchConversations(searchTerm)
    }, 300)

    return () => clearTimeout(delayDebounceFn)
  }, [searchTerm, searchConversations])

  const handleNewConversation = () => {
    try {
      console.log('Creating new draft conversation...')
      const draftId = createDraftConversation('New Conversation')
      console.log('Created draft conversation with ID:', draftId)
      setCurrentConversation(draftId)
      // Navigate to chat page if not already there
      if (window.location.pathname !== '/') {
        navigate('/')
      }
      console.log('Draft conversation setup complete')
    } catch (error) {
      console.error('Failed to create draft conversation:', error)
      alert('Failed to create conversation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  const handleSelectConversation = async (id: string) => {
    setCurrentConversation(id)
    await loadMessages(id)
    // Navigate to chat page if not already there
    if (window.location.pathname !== '/') {
      navigate('/')
    }
  }

  const handleDeleteConversation = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm('Are you sure you want to delete this conversation?')) {
      await deleteConversation(id)
    }
  }

  const handleEditStart = (id: string, title: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setEditingId(id)
    setEditTitle(title)
  }

  const handleEditSave = async (id: string) => {
    if (editTitle.trim()) {
      try {
        await window.electronAPI.db.updateConversation(id, editTitle.trim())
        useAppStore.getState().loadConversations()
      } catch (error) {
        console.error('Failed to update conversation:', error)
      }
    }
    setEditingId(null)
    setEditTitle('')
  }

  const handleEditCancel = () => {
    setEditingId(null)
    setEditTitle('')
  }

  const handleCheckForUpdates = async () => {
    if (!window.electronAPI?.updater) {
      console.log('Updater not available')
      return
    }

    setUpdateStatus('checking')
    setUpdateError(null)

    try {
      const result = await window.electronAPI.updater.checkForUpdates()
      if (result.available) {
        setUpdateStatus('available')
      } else {
        setUpdateStatus('idle')
        if (result.error) {
          setUpdateError(result.error)
          setUpdateStatus('error')
        }
      }
    } catch (error) {
      console.error('Error checking for updates:', error)
      setUpdateError(error instanceof Error ? error.message : 'Unknown error')
      setUpdateStatus('error')
    }
  }

  const handleDownloadAndInstall = async () => {
    if (!window.electronAPI?.updater) {
      return
    }

    setUpdateStatus('downloading')

    try {
      const result = await window.electronAPI.updater.downloadAndInstall()
      if (result.success) {
        setUpdateStatus('downloaded')
        setToast({ message: 'Update downloaded successfully! Restart to apply.', type: 'success' })
      } else {
        setUpdateError(result.error || 'Failed to download update')
        setUpdateStatus('error')
        setToast({ message: result.error || 'Failed to download update', type: 'error' })
      }
    } catch (error) {
      console.error('Error downloading update:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setUpdateError(errorMessage)
      setUpdateStatus('error')
      setToast({ message: errorMessage, type: 'error' })
    }
  }

  // Set up update event listeners
  useEffect(() => {
    if (!window.electronAPI?.updater) {
      return
    }

    const cleanup: (() => void)[] = []

    // Set up event listeners
    window.electronAPI.updater.onCheckingForUpdate(() => {
      setUpdateStatus('checking')
    })

    window.electronAPI.updater.onUpdateAvailable((info) => {
      console.log('Update available:', info)
      setUpdateStatus('available')
    })

    window.electronAPI.updater.onUpdateNotAvailable(() => {
      setUpdateStatus('idle')
    })

    window.electronAPI.updater.onError((error) => {
      console.error('Update error:', error)
      setUpdateError(error)
      setUpdateStatus('error')
      setToast({ message: error, type: 'error' })
    })

    window.electronAPI.updater.onDownloadProgress((progress) => {
      console.log('Download progress:', progress.percent + '%')
      setUpdateStatus('downloading')
    })

    window.electronAPI.updater.onUpdateDownloaded((info) => {
      console.log('Update downloaded:', info)
      setUpdateStatus('downloaded')
      setToast({ message: 'Update downloaded successfully! Restart to apply.', type: 'success' })
    })

    return () => {
      cleanup.forEach(fn => fn())
    }
  }, [])

  return (
    <aside className="flex flex-col w-72 bg-gray-800/60 backdrop-blur-lg border-r border-tertiary h-full md:w-72">
      {/* Streamlined Header - No Logo */}
      <header className="flex items-center justify-between h-12 px-4 border-b border-tertiary/50">
        <h2 className="text-sm font-semibold text-supplement1">Conversations</h2>
        <button
          onClick={handleNewConversation}
          className="u1-button-icon w-8 h-8"
          title="New Conversation"
        >
          <Plus className="h-4 w-4" />
        </button>
      </header>

      {/* Search Only */}
      <div className="p-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="u1-input-search w-full pl-10 pr-4 py-2 text-sm"
          />
        </div>

        {/* Filter Links */}
        <div className="flex justify-center gap-3 px-3 mt-1 text-xs">
          <button
            onClick={() => {
              setConversationFilter('all')
              loadConversationsWithFilter('all')
            }}
            className={`hover:text-primary transition-colors ${
              conversationFilter === 'all' ? 'text-primary underline' : 'text-gray-400'
            }`}
          >
            all
          </button>
          <button
            onClick={() => {
              setConversationFilter('pinned')
              loadConversationsWithFilter('pinned')
            }}
            className={`hover:text-primary transition-colors ${
              conversationFilter === 'pinned' ? 'text-primary underline' : 'text-gray-400'
            }`}
          >
            pinned
          </button>
          <button
            onClick={() => {
              setConversationFilter('with-docs')
              loadConversationsWithFilter('with-docs')
            }}
            className={`hover:text-primary transition-colors ${
              conversationFilter === 'with-docs' ? 'text-primary underline' : 'text-gray-400'
            }`}
          >
            w/generated doc
          </button>
        </div>
      </div>

      {/* Conversations List */}
      <nav className="flex-1 overflow-y-auto px-2 min-h-0">
        {/* Pinned Conversations */}
        {pinnedConversations.length > 0 && (
          <div className="mb-4">
            <h3 className="text-xs uppercase font-semibold text-gray-400 px-4 mb-2">Pinned</h3>
            <div className="space-y-1">
              {pinnedConversations.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  currentConversationId={currentConversationId}
                  handleSelectConversation={handleSelectConversation}
                  handleEditStart={handleEditStart}
                  handleDeleteConversation={handleDeleteConversation}
                  editingId={editingId}
                  editTitle={editTitle}
                  handleEditSave={handleEditSave}
                  handleEditCancel={handleEditCancel}
                  setEditTitle={setEditTitle}
                />
              ))}
            </div>
          </div>
        )}

        {/* Grouped Conversations */}
        {conversationGroups.today.length > 0 && (
          <div className="mb-4">
            <h3 className="text-xs uppercase font-semibold text-gray-400 px-4 mb-2">Today</h3>
            <div className="space-y-1">
              {conversationGroups.today.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  currentConversationId={currentConversationId}
                  handleSelectConversation={handleSelectConversation}
                  handleEditStart={handleEditStart}
                  handleDeleteConversation={handleDeleteConversation}
                  editingId={editingId}
                  editTitle={editTitle}
                  handleEditSave={handleEditSave}
                  handleEditCancel={handleEditCancel}
                  setEditTitle={setEditTitle}
                />
              ))}
            </div>
          </div>
        )}

        {conversationGroups.yesterday.length > 0 && (
          <div className="mb-4">
            <h3 className="text-xs uppercase font-semibold text-gray-400 px-4 mb-2">Yesterday</h3>
            <div className="space-y-1">
              {conversationGroups.yesterday.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  currentConversationId={currentConversationId}
                  handleSelectConversation={handleSelectConversation}
                  handleEditStart={handleEditStart}
                  handleDeleteConversation={handleDeleteConversation}
                  editingId={editingId}
                  editTitle={editTitle}
                  handleEditSave={handleEditSave}
                  handleEditCancel={handleEditCancel}
                  setEditTitle={setEditTitle}
                />
              ))}
            </div>
          </div>
        )}

        {conversationGroups.lastWeek.length > 0 && (
          <div className="mb-4">
            <h3 className="text-xs uppercase font-semibold text-gray-400 px-4 mb-2">Last 7 Days</h3>
            <div className="space-y-1">
              {conversationGroups.lastWeek.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  currentConversationId={currentConversationId}
                  handleSelectConversation={handleSelectConversation}
                  handleEditStart={handleEditStart}
                  handleDeleteConversation={handleDeleteConversation}
                  editingId={editingId}
                  editTitle={editTitle}
                  handleEditSave={handleEditSave}
                  handleEditCancel={handleEditCancel}
                  setEditTitle={setEditTitle}
                />
              ))}
            </div>
          </div>
        )}

        {conversationGroups.older.length > 0 && (
          <div className="mb-4">
            <h3 className="text-xs uppercase font-semibold text-gray-400 px-4 mb-2">Older</h3>
            <div className="space-y-1">
              {conversationGroups.older.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  currentConversationId={currentConversationId}
                  handleSelectConversation={handleSelectConversation}
                  handleEditStart={handleEditStart}
                  handleDeleteConversation={handleDeleteConversation}
                  editingId={editingId}
                  editTitle={editTitle}
                  handleEditSave={handleEditSave}
                  handleEditCancel={handleEditCancel}
                  setEditTitle={setEditTitle}
                />
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Footer */}
      <footer className="flex-shrink-0 p-4 border-t border-tertiary space-y-3">
        {/* Network Status and Private Mode */}
        <NetworkStatus />


        {updateStatus === 'available' && (
          <button
            onClick={handleDownloadAndInstall}
            className="w-full flex items-center gap-2 px-4 py-3 mt-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm font-medium transition-colors text-white"
          >
            Update Available! Click to Install
          </button>
        )}
        {updateStatus === 'downloading' && (
          <div className="w-full flex items-center gap-2 px-4 py-3 mt-2 bg-blue-600 rounded-lg text-sm font-medium text-white">
            Downloading Update...
          </div>
        )}
        {updateStatus === 'error' && updateError && (
          <div className="w-full flex items-center gap-2 px-4 py-3 mt-2 bg-red-600 rounded-lg text-sm font-medium text-white">
            Update Error: {updateError}
          </div>
        )}
      </footer>

      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
    </aside>
  );
}

export default Sidebar
