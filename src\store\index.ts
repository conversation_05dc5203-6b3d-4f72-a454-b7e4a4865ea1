import { create } from 'zustand'
import { Conversation, Message, Settings, OpenRouterModel, FileRecord, Artifact, ArtifactsState } from '../types'
import { openRouterService } from '../services/openrouter'
import { useNetworkStore } from '../stores/networkStore'
import { localModelService } from '../services/localModelService'
import { modelUpdateLogic } from '../services/modelUpdateLogic'

// Helper function to check if model supports vision
const isVisionModel = (modelId: string): boolean => {
  const visionModels = [
    'openai/gpt-4-vision-preview',
    'openai/gpt-4o',
    'openai/gpt-4o-mini',
    'anthropic/claude-3-opus',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-haiku',
    'anthropic/claude-3-5-sonnet',
    'google/gemini-pro-vision',
    'google/gemini-1.5-pro',
    'google/gemini-1.5-flash'
  ]
  return visionModels.some(model => modelId.includes(model.split('/')[1]))
}

// Utility function to generate conversation title from first message
const generateConversationTitle = (content: string): string => {
  // Remove extra whitespace and newlines
  const cleaned = content.trim().replace(/\s+/g, ' ')

  // Truncate to 50 characters and add ellipsis if needed
  if (cleaned.length <= 50) {
    return cleaned
  }

  // Find the last space before the 50 character limit to avoid cutting words
  const truncated = cleaned.substring(0, 50)
  const lastSpace = truncated.lastIndexOf(' ')

  if (lastSpace > 30) { // Only use word boundary if it's not too short
    return truncated.substring(0, lastSpace) + '...'
  }

  return truncated + '...'
}

// Helper function to determine if we should use local models
const shouldUseLocalModel = (selectedModel?: string): boolean => {
  const networkState = useNetworkStore.getState()

  // Check if selected model is local (contains ":")
  if (selectedModel && selectedModel.includes(':')) {
    // Local models have format "provider:modelname"
    return true
  }

  // If private mode is on and no specific model selected, use local models
  if (networkState.isPrivateMode) {
    return true
  }

  // If private mode is off and no local model selected, use external models
  return false
}

// Helper function to check if local models are available
const areLocalModelsAvailable = (): boolean => {
  const networkState = useNetworkStore.getState()
  return networkState.localModelsAvailable
}

// Helper function to build multi-modal message content
const buildMultiModalContent = async (
  textContent: string,
  attachedFiles?: FileRecord[]
): Promise<any> => {
  if (!attachedFiles || attachedFiles.length === 0) {
    return textContent
  }

  const imageFiles = attachedFiles.filter(file => file.file_type === 'image')

  if (imageFiles.length === 0) {
    return textContent
  }

  // Build multi-modal content array
  const content = [
    {
      type: 'text',
      text: textContent
    }
  ]

  // Add images
  for (const imageFile of imageFiles) {
    try {
      if (window.electronAPI?.files) {
        const base64Content = await window.electronAPI.files.getFileContent(imageFile.filepath)
        if (base64Content) {
          content.push({
            type: 'image_url',
            image_url: {
              url: `data:${imageFile.mime_type || 'image/png'};base64,${base64Content}`,
              detail: 'high' // Use high detail for better analysis
            }
          })
        }
      }
    } catch (error) {
      console.error('Error loading image for vision model:', error)
    }
  }

  return content
}

// Helper function to build file context efficiently
const buildFileContext = async (attachedFiles?: FileRecord[], fileReferences?: string[]): Promise<string> => {
  let fileContext = ''

  try {
    // Add attached files context
    if (attachedFiles && attachedFiles.length > 0) {
      fileContext += '\n\n--- Attached Files ---\n'
      for (const file of attachedFiles) {
        fileContext += `File: ${file.filename}\n`

        // Process content on-demand if not already processed
        if (!file.extracted_content && window.electronAPI?.files) {
          try {
            await window.electronAPI.files.processFileContent(file.id)
            // Refetch the file to get updated content
            const updatedFiles = await window.electronAPI.files.getIndexedFiles()
            const updatedFile = updatedFiles.find(f => f.id === file.id)
            if (updatedFile?.extracted_content) {
              file.extracted_content = updatedFile.extracted_content
            }
          } catch (error) {
            console.error('Error processing file content on-demand:', error)
          }
        }

        if (file.extracted_content) {
          // Limit content to 10000 chars for better context while maintaining efficiency
          const maxLength = 10000
          fileContext += `Content: ${file.extracted_content.substring(0, maxLength)}${file.extracted_content.length > maxLength ? '...\n[Content truncated for length]' : ''}\n\n`
        } else {
          fileContext += `Type: ${file.file_type}, Size: ${file.file_size} bytes\n[Note: File not vectorized - content not available for AI processing]\n\n`
        }
      }
    }

    // Add referenced files context
    if (fileReferences && fileReferences.length > 0 && window.electronAPI?.files) {
      for (const reference of fileReferences) {
        const filename = reference.substring(1) // Remove @
        // Use search to find the file efficiently
        const searchResults = await window.electronAPI.files.searchFiles(filename, 1)
        const referencedFile = searchResults.find(f => f.filename === filename)

        if (referencedFile && referencedFile.extracted_content) {
          fileContext += `\n--- Referenced File: ${filename} ---\n`
          const maxLength = 10000
          fileContext += referencedFile.extracted_content.substring(0, maxLength)
          if (referencedFile.extracted_content.length > maxLength) {
            fileContext += '...\n[Content truncated for length]'
          }
          fileContext += '\n\n'
        }
      }
    }
  } catch (error) {
    console.error('Error building file context:', error)
  }

  return fileContext
}

interface AppState {
  // Conversations
  conversations: Conversation[]
  currentConversationId: string | null
  draftConversationId: string | null // For unsaved conversations
  messages: Message[]

  // Models
  models: OpenRouterModel[]

  // Settings
  settings: Settings

  // UI State
  sidebarOpen: boolean
  isLoading: boolean
  streamingMessage: string | null
  searchTerm: string
  conversationFilter: 'all' | 'pinned' | 'with-docs'
  activeIconBarItem: string

  // Artifacts State
  artifacts: {
    isOpen: boolean
    isFullscreen: boolean
    currentArtifact: Artifact | null
    artifacts: Artifact[]
    sidebarWidth: number
  }
  
  // Actions
  setConversations: (conversations: Conversation[]) => void
  setCurrentConversation: (id: string | null) => void
  setMessages: (messages: Message[]) => void
  addMessage: (message: Message) => void
  setModels: (models: OpenRouterModel[]) => void
  updateSettings: (settings: Partial<Settings>) => void
  setSidebarOpen: (open: boolean) => void
  setLoading: (loading: boolean) => void
  setStreamingMessage: (message: string | null) => void
  setSearchTerm: (term: string) => void
  setConversationFilter: (filter: 'all' | 'pinned' | 'with-docs') => void
  setActiveIconBarItem: (item: string) => void

  // Artifacts Actions
  openArtifact: (artifact: Artifact) => void
  closeArtifacts: () => void
  toggleArtifactsFullscreen: () => void
  addArtifact: (artifact: Artifact) => void
  removeArtifact: (id: string) => void
  setActiveArtifact: (id: string) => void
  setCurrentArtifact: (artifact: Artifact) => void
  setArtifactsSidebarWidth: (width: number) => void
  clearArtifacts: () => void
  
  // Async actions
  loadConversations: () => Promise<void>
  loadConversationsWithFilter: (filter: 'all' | 'pinned' | 'with-docs') => Promise<void>
  loadMessages: (conversationId: string) => Promise<void>
  createConversation: (title: string) => Promise<string>
  createDraftConversation: (title: string) => string
  saveDraftConversation: (draftId: string) => Promise<string>
  deleteConversation: (id: string) => Promise<void>
  sendMessage: (content: string, conversationId?: string, attachedFiles?: FileRecord[]) => Promise<void>
  sendStreamingMessage: (content: string, conversationId?: string, attachedFiles?: FileRecord[]) => Promise<void>
  loadModels: () => Promise<void>
  searchConversations: (term: string) => Promise<void>
  togglePinMessage: (messageId: string) => Promise<void>
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  conversations: [],
  currentConversationId: null,
  draftConversationId: null,
  messages: [],
  models: [],
  settings: {
    temperature: 0.7,
    maxTokens: 4096,
    theme: 'dark',
    topP: 0.95,
    topK: 30,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: '',
    favoriteModels: [],
    modelFilter: 'all',
  },
  sidebarOpen: true,
  isLoading: false,
  streamingMessage: null,
  searchTerm: '',
  conversationFilter: 'all',
  activeIconBarItem: 'chat',

  // Artifacts initial state
  artifacts: {
    isOpen: false,
    isFullscreen: false,
    currentArtifact: null,
    artifacts: [],
    sidebarWidth: 400
  },
  
  // Sync actions
  setConversations: (conversations) => set({ conversations }),
  setCurrentConversation: (id) => set((state) => ({
    currentConversationId: id,
    // Clear artifacts when switching conversations
    artifacts: {
      ...state.artifacts,
      artifacts: [],
      currentArtifact: null,
      isOpen: false,
      isFullscreen: false
    }
  })),
  setMessages: (messages) => set({ messages }),
  addMessage: (message) => set((state) => ({ messages: [...state.messages, message] })),
  setModels: (models) => set({ models }),
  updateSettings: (newSettings) => set((state) => ({ 
    settings: { ...state.settings, ...newSettings } 
  })),
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  setLoading: (loading) => set({ isLoading: loading }),
  setStreamingMessage: (message) => set({ streamingMessage: message }),
  setSearchTerm: (term) => set({ searchTerm: term }),
  setConversationFilter: (filter) => set({ conversationFilter: filter }),
  setActiveIconBarItem: (item) => set({ activeIconBarItem: item }),

  // Artifacts actions
  openArtifact: (artifact) => set((state) => ({
    artifacts: {
      ...state.artifacts,
      isOpen: true,
      currentArtifact: artifact,
      artifacts: state.artifacts.artifacts.map(a =>
        a.id === artifact.id ? { ...a, isActive: true } : { ...a, isActive: false }
      )
    }
  })),

  closeArtifacts: () => set((state) => ({
    artifacts: {
      ...state.artifacts,
      isOpen: false,
      isFullscreen: false,
      currentArtifact: null,
      artifacts: state.artifacts.artifacts.map(a => ({ ...a, isActive: false }))
    }
  })),

  toggleArtifactsFullscreen: () => set((state) => ({
    artifacts: {
      ...state.artifacts,
      isFullscreen: !state.artifacts.isFullscreen
    }
  })),

  addArtifact: (artifact) => set((state) => ({
    artifacts: {
      ...state.artifacts,
      artifacts: [...state.artifacts.artifacts, artifact]
    }
  })),

  removeArtifact: (id) => set((state) => ({
    artifacts: {
      ...state.artifacts,
      artifacts: state.artifacts.artifacts.filter(a => a.id !== id),
      currentArtifact: state.artifacts.currentArtifact?.id === id ? null : state.artifacts.currentArtifact
    }
  })),

  setActiveArtifact: (id) => set((state) => {
    const artifact = state.artifacts.artifacts.find(a => a.id === id)
    return {
      artifacts: {
        ...state.artifacts,
        currentArtifact: artifact || null,
        artifacts: state.artifacts.artifacts.map(a =>
          a.id === id ? { ...a, isActive: true } : { ...a, isActive: false }
        )
      }
    }
  }),

  setCurrentArtifact: (artifact) => set((state) => ({
    artifacts: {
      ...state.artifacts,
      isOpen: true,
      isFullscreen: true, // Automatically trigger fullscreen
      currentArtifact: artifact,
      artifacts: state.artifacts.artifacts.map(a =>
        a.id === artifact.id ? { ...a, isActive: true } : { ...a, isActive: false }
      )
    }
  })),

  setArtifactsSidebarWidth: (width) => set((state) => ({
    artifacts: {
      ...state.artifacts,
      sidebarWidth: width
    }
  })),

  clearArtifacts: () => set((state) => ({
    artifacts: {
      ...state.artifacts,
      artifacts: [],
      currentArtifact: null,
      isOpen: false,
      isFullscreen: false
    }
  })),
  
  // Async actions
  loadConversations: async () => {
    try {
      const conversations = await window.electronAPI.db.getConversations()
      set({ conversations })
    } catch (error) {
      console.error('Failed to load conversations:', error)
    }
  },

  loadConversationsWithFilter: async (filter: 'all' | 'pinned' | 'with-docs') => {
    try {
      let conversations
      switch (filter) {
        case 'pinned':
          const allConversations = await window.electronAPI.db.getConversations()
          conversations = allConversations.filter(c => c.is_pinned === 1)
          break
        case 'with-docs':
          conversations = await window.electronAPI.db.getConversationsWithArtifacts()
          break
        default:
          conversations = await window.electronAPI.db.getConversations()
      }
      set({ conversations, conversationFilter: filter })
    } catch (error) {
      console.error('Failed to load conversations with filter:', error)
    }
  },
  
  loadMessages: async (conversationId: string) => {
    try {
      const messages = await window.electronAPI.db.getMessages(conversationId)

      // Load file attachments for each message
      const messagesWithAttachments = await Promise.all(
        messages.map(async (message) => {
          try {
            const messageFiles = await window.electronAPI.files.getMessageFiles(message.id)
            return {
              ...message,
              attachments: messageFiles.map(fileWithAttachment => ({
                id: fileWithAttachment.attachment_id,
                message_id: message.id,
                file_id: fileWithAttachment.id,
                attachment_type: fileWithAttachment.attachment_type,
                created_at: fileWithAttachment.attachment_created_at || fileWithAttachment.created_at,
                file: {
                  id: fileWithAttachment.id,
                  filename: fileWithAttachment.filename,
                  filepath: fileWithAttachment.filepath,
                  file_type: fileWithAttachment.file_type,
                  file_size: fileWithAttachment.file_size,
                  content_hash: fileWithAttachment.content_hash,
                  extracted_content: fileWithAttachment.extracted_content,
                  metadata: fileWithAttachment.metadata,
                  created_at: fileWithAttachment.created_at,
                  updated_at: fileWithAttachment.updated_at
                }
              }))
            }
          } catch (error) {
            console.error('Failed to load attachments for message:', message.id, error)
            return message
          }
        })
      )

      set({ messages: messagesWithAttachments })
    } catch (error) {
      console.error('Failed to load messages:', error)
    }
  },
  
  createConversation: async (title: string) => {
    try {
      console.log('Store: Creating conversation with title:', title)

      if (!window.electronAPI?.db) {
        throw new Error('Electron API not available')
      }

      const id = await window.electronAPI.db.createConversation(title)
      console.log('Store: Got conversation ID:', id)

      await get().loadConversations()
      console.log('Store: Reloaded conversations')

      return id
    } catch (error) {
      console.error('Failed to create conversation:', error)
      throw error
    }
  },

  createDraftConversation: (title: string) => {
    // Generate a temporary ID for the draft conversation
    const draftId = `draft-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    console.log('Store: Creating draft conversation with ID:', draftId)

    set({
      draftConversationId: draftId,
      currentConversationId: draftId,
      messages: []
    })

    return draftId
  },

  saveDraftConversation: async (draftId: string) => {
    try {
      const { draftConversationId, messages } = get()

      if (draftConversationId !== draftId) {
        throw new Error('Draft conversation ID mismatch')
      }

      if (!window.electronAPI?.db) {
        throw new Error('Electron API not available')
      }

      // Create the conversation in the database
      const realId = await window.electronAPI.db.createConversation('New Conversation')
      console.log('Store: Saved draft conversation with real ID:', realId)

      // Update the state to use the real ID
      set({
        draftConversationId: null,
        currentConversationId: realId
      })

      // Reload conversations to show the new one
      await get().loadConversations()

      return realId
    } catch (error) {
      console.error('Failed to save draft conversation:', error)
      throw error
    }
  },
  
  deleteConversation: async (id: string) => {
    try {
      await window.electronAPI.db.deleteConversation(id)
      await get().loadConversations()
      if (get().currentConversationId === id) {
        set({ currentConversationId: null, messages: [] })
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error)
    }
  },
  
  sendMessage: async (content: string, conversationId?: string, attachedFiles?: FileRecord[]) => {
    const { currentConversationId, draftConversationId, settings, messages } = get()
    let targetConversationId = conversationId || currentConversationId
    if (!targetConversationId) return

    try {
      set({ isLoading: true })

      // Handle draft conversation - save it to database before sending message
      if (draftConversationId && targetConversationId === draftConversationId) {
        console.log('Converting draft conversation to real conversation')
        targetConversationId = await get().saveDraftConversation(draftConversationId)
      }

      // Only process file context when there are actual files or references
      let fileContext = ''
      const hasAttachments = attachedFiles && attachedFiles.length > 0
      const fileReferences = content.match(/@([^\s@]+)/g)

      if (hasAttachments || fileReferences) {
        fileContext = await buildFileContext(attachedFiles, fileReferences)

        // Check for unvectorized files and log warning
        if (attachedFiles) {
          const unvectorizedFiles = attachedFiles.filter(file => !file.extracted_content)
          if (unvectorizedFiles.length > 0) {
            console.warn(`Warning: ${unvectorizedFiles.length} attached file(s) are not vectorized and won't be processed by AI:`,
              unvectorizedFiles.map(f => f.filename))
          }
        }
      }

      // Combine content with file context
      const fullContent = content + fileContext

      // Add user message (store original content, but send fullContent to API)
      const userMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: targetConversationId,
        role: 'user',
        content, // Store original user message without file context for clean display
      }

      const messageId = await window.electronAPI.db.addMessage(targetConversationId, userMessage)

      // Add file attachments if any
      if (attachedFiles && attachedFiles.length > 0) {
        for (const file of attachedFiles) {
          try {
            await window.electronAPI.files.addFileAttachment(messageId, file.id, 'attachment')
            console.log('File attachment added:', file.filename)
          } catch (error) {
            console.error('Error adding file attachment:', error)
          }
        }
      }

      await get().loadMessages(targetConversationId)

      // Auto-name conversation based on first user message
      const currentMessages = get().messages
      const userMessages = currentMessages.filter(m => m.role === 'user')
      if (userMessages.length === 1) {
        // This is the first user message, update conversation title
        const newTitle = generateConversationTitle(content)
        try {
          await window.electronAPI.db.updateConversation(targetConversationId, newTitle)
          await get().loadConversations() // Refresh conversations list to show new title
        } catch (error) {
          console.error('Failed to update conversation title:', error)
        }
      }

      // Check private mode and model availability
      const useLocalModel = shouldUseLocalModel(settings.selectedModel)

      if (useLocalModel) {
        // Private mode - use local models
        if (!areLocalModelsAvailable()) {
          const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Private mode is enabled but no local models are available. Please install Ollama or LM Studio and download models to continue chatting privately.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
          await get().loadMessages(targetConversationId)
          return
        }

        if (!settings.selectedModel) {
          const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Please select a local model to continue chatting in private mode.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
          await get().loadMessages(targetConversationId)
          return
        }
      } else {
        // Non-private mode - use external models via OpenRouter
        if (!settings.openRouterApiKey) {
          const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Please set your OpenRouter API key in settings to start chatting with AI models.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
          await get().loadMessages(targetConversationId)
          return
        }

        // Set API key for OpenRouter
        openRouterService.setApiKey(settings.openRouterApiKey)
      }

      // Prepare conversation history
      const conversationMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      }))

      // Add system prompt if configured
      if (settings.systemPrompt && settings.systemPrompt.trim()) {
        conversationMessages.unshift({
          role: 'system' as const,
          content: settings.systemPrompt.trim(),
        })
      }

      // Check if current model supports vision
      const currentModel = settings.selectedModel
      const supportsVision = currentModel ? isVisionModel(currentModel) : false

      // Build message content based on model capabilities
      let messageContent: any = fullContent
      if (supportsVision && attachedFiles && attachedFiles.some(f => f.file_type === 'image')) {
        messageContent = await buildMultiModalContent(content, attachedFiles)
        console.log('Using multi-modal content for vision model:', currentModel)
      }

      // Add the new user message
      conversationMessages.push({
        role: 'user' as const,
        content: messageContent,
      })

      try {
        let response: string

        if (useLocalModel) {
          // Use local model service
          response = await localModelService.sendMessage(
            settings.selectedModel!,
            conversationMessages
          )
        } else {
          // Use OpenRouter service
          response = await openRouterService.createChatCompletion({
            model: settings.selectedModel || 'openai/gpt-3.5-turbo',
            messages: conversationMessages,
            temperature: settings.temperature,
            max_tokens: settings.maxTokens,
            top_p: settings.topP,
            top_k: settings.topK,
            frequency_penalty: settings.frequencyPenalty,
            presence_penalty: settings.presencePenalty,
          })
        }

        const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: targetConversationId,
          role: 'assistant',
          content: response,
          model: settings.selectedModel,
        }

        await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
        await get().loadMessages(targetConversationId)

      } catch (apiError) {
        console.error('OpenRouter API error:', apiError)

        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: targetConversationId,
          role: 'assistant',
          content: `Error: ${apiError instanceof Error ? apiError.message : 'Failed to get response from AI model'}`,
          model: 'system',
        }

        await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
        await get().loadMessages(targetConversationId)
      }

    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      set({ isLoading: false })
    }
  },

  // Streaming message implementation
  sendStreamingMessage: async (content: string, conversationId?: string, attachedFiles?: FileRecord[]) => {
    const { currentConversationId, draftConversationId, messages, settings } = get()
    let targetConversationId = conversationId || currentConversationId

    if (!targetConversationId) {
      console.error('No conversation selected')
      return
    }

    set({ isLoading: true, streamingMessage: '' })

    try {
      // Handle draft conversation - save it to database before sending message
      if (draftConversationId && targetConversationId === draftConversationId) {
        console.log('Converting draft conversation to real conversation')
        targetConversationId = await get().saveDraftConversation(draftConversationId)
      }
      // Only process file context when there are actual files or references
      let fileContext = ''
      const hasAttachments = attachedFiles && attachedFiles.length > 0
      const fileReferences = content.match(/@([^\s@]+)/g)

      if (hasAttachments || fileReferences) {
        fileContext = await buildFileContext(attachedFiles, fileReferences)

        // Check for unvectorized files and log warning
        if (attachedFiles) {
          const unvectorizedFiles = attachedFiles.filter(file => !file.extracted_content)
          if (unvectorizedFiles.length > 0) {
            console.warn(`Warning: ${unvectorizedFiles.length} attached file(s) are not vectorized and won't be processed by AI:`,
              unvectorizedFiles.map(f => f.filename))
          }
        }
      }

      // Combine content with file context
      const fullContent = content + fileContext

      // Add user message to database (store original content for clean display)
      const userMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: targetConversationId,
        role: 'user',
        content, // Store original user message without file context
        model: undefined,
      }

      const messageId = await window.electronAPI.db.addMessage(targetConversationId, userMessage)

      // Add file attachments if any
      if (attachedFiles && attachedFiles.length > 0) {
        for (const file of attachedFiles) {
          try {
            await window.electronAPI.files.addFileAttachment(messageId, file.id, 'attachment')
            console.log('File attachment added:', file.filename)
          } catch (error) {
            console.error('Error adding file attachment:', error)
          }
        }
      }
      await get().loadMessages(targetConversationId)

      // Auto-name conversation based on first user message
      const currentMessages = get().messages
      const userMessages = currentMessages.filter(m => m.role === 'user')
      if (userMessages.length === 1) {
        // This is the first user message, update conversation title
        const newTitle = generateConversationTitle(content)
        try {
          await window.electronAPI.db.updateConversation(targetConversationId, newTitle)
          await get().loadConversations() // Refresh conversations list to show new title
        } catch (error) {
          console.error('Failed to update conversation title:', error)
        }
      }

      // Check private mode and model availability
      const useLocalModel = shouldUseLocalModel(settings.selectedModel)

      if (useLocalModel) {
        // Private mode - use local models
        if (!areLocalModelsAvailable()) {
          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Private mode is enabled but no local models are available. Please install Ollama or LM Studio and download models to continue chatting privately.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          await get().loadMessages(targetConversationId)
          set({ isLoading: false, streamingMessage: null })
          return
        }

        if (!settings.selectedModel) {
          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Please select a local model to continue chatting in private mode.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          await get().loadMessages(targetConversationId)
          set({ isLoading: false, streamingMessage: null })
          return
        }
      } else {
        // Non-private mode - use external models via OpenRouter
        if (!settings.openRouterApiKey) {
          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Please set your OpenRouter API key in settings to use AI models.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          await get().loadMessages(targetConversationId)
          set({ isLoading: false, streamingMessage: null })
          return
        }

        // Check if model is selected
        if (!settings.selectedModel) {
          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: 'Please select an AI model in the chat settings to continue.',
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          await get().loadMessages(targetConversationId)
          set({ isLoading: false, streamingMessage: null })
          return
        }

        // Set API key and validate it
        openRouterService.setApiKey(settings.openRouterApiKey)

        // Validate API key before proceeding
        const validation = await openRouterService.validateApiKey()
        if (!validation.valid) {
          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: targetConversationId,
            role: 'assistant',
            content: `API Key Error: ${validation.error}`,
            model: 'system',
          }

          await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          await get().loadMessages(targetConversationId)
          set({ isLoading: false, streamingMessage: null })
          return
        }
      }

      const conversationMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      }))

      // Add system prompt if configured
      if (settings.systemPrompt && settings.systemPrompt.trim()) {
        conversationMessages.unshift({
          role: 'system' as const,
          content: settings.systemPrompt.trim(),
        })
      }

      // Check if current model supports vision
      const currentModel = settings.selectedModel
      const supportsVision = currentModel ? isVisionModel(currentModel) : false

      // Build message content based on model capabilities
      let messageContent: any = fullContent
      if (supportsVision && attachedFiles && attachedFiles.some(f => f.file_type === 'image')) {
        messageContent = await buildMultiModalContent(content, attachedFiles)
        console.log('Using multi-modal content for vision model (streaming):', currentModel)
      }

      // Add the new user message
      conversationMessages.push({
        role: 'user' as const,
        content: messageContent,
      })

      let streamingContent = ''

      // Add timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        console.error('Streaming request timed out after 60 seconds')
        const timeoutError = new Error('Request timed out. Please try again.')
        set({
          isLoading: false,
          streamingMessage: null
        })

        // Add timeout error message
        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: targetConversationId,
          role: 'assistant',
          content: 'Request timed out. Please check your connection and try again.',
          model: 'system',
        }

        window.electronAPI.db.addMessage(targetConversationId, errorMessage)
          .then(() => get().loadMessages(targetConversationId))
          .catch(console.error)
      }, 60000) // 60 second timeout

      // Start streaming based on model type
      if (useLocalModel) {
        // Use local model service with streaming
        await localModelService.sendMessage(
          settings.selectedModel!,
          conversationMessages,
          // onChunk callback for streaming
          (chunk: string) => {
            streamingContent += chunk
            set({ streamingMessage: streamingContent })
          }
        )

        clearTimeout(timeoutId)

        // Save the complete message to database
        const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: targetConversationId,
          role: 'assistant',
          content: streamingContent,
          model: settings.selectedModel,
        }

        await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
        await get().loadMessages(targetConversationId)
        set({ streamingMessage: null })
      } else {
        // Use OpenRouter service with streaming
        await openRouterService.createStreamingChatCompletion(
          {
            model: settings.selectedModel || 'openai/gpt-3.5-turbo',
            messages: conversationMessages,
            temperature: settings.temperature,
            max_tokens: settings.maxTokens,
            top_p: settings.topP,
            top_k: settings.topK,
            frequency_penalty: settings.frequencyPenalty,
            presence_penalty: settings.presencePenalty,
          },
          // onChunk
          (chunk: string) => {
            streamingContent += chunk
            set({ streamingMessage: streamingContent })
          },
          // onComplete
          async () => {
            clearTimeout(timeoutId)

            // Save the complete message to database
            const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
              conversation_id: targetConversationId,
              role: 'assistant',
              content: streamingContent,
              model: settings.selectedModel,
            }

            await window.electronAPI.db.addMessage(targetConversationId, assistantMessage)
            await get().loadMessages(targetConversationId)
            set({ streamingMessage: null })
          },
          // onError
          async (error: Error) => {
            clearTimeout(timeoutId)
            console.error('Streaming error:', error)

            const errorMessage: Omit<Message, 'id' | 'created_at'> = {
              conversation_id: targetConversationId,
              role: 'assistant',
              content: `Error: ${error.message}`,
              model: 'system',
            }

            await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
            await get().loadMessages(targetConversationId)
            set({ streamingMessage: null })
          }
        )
      }

    } catch (error) {
      console.error('Failed to send streaming message:', error)
      set({ streamingMessage: null })

      // Add error message to chat
      const errorMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: targetConversationId,
        role: 'assistant',
        content: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        model: 'system',
      }

      try {
        await window.electronAPI.db.addMessage(targetConversationId, errorMessage)
        await get().loadMessages(targetConversationId)
      } catch (dbError) {
        console.error('Failed to save error message:', dbError)
      }
    } finally {
      set({ isLoading: false })
    }
  },

  loadModels: async () => {
    try {
      const { settings } = get()
      if (!settings.openRouterApiKey) {
        console.log('No API key available for loading models')
        return
      }

      // Check if we should use OTA model updates
      const { shouldUpdate, latestVersion } = await modelUpdateLogic.shouldUpdate()
      let models: OpenRouterModel[] = []
      let updateInfo: { version?: string; timestamp?: string } | null = null

      if (shouldUpdate && latestVersion) {
        console.log(`Fetching updated models from manifest v${latestVersion}...`)
        const enhancedModels = await modelUpdateLogic.getModels(settings.openRouterApiKey)
        models = enhancedModels as OpenRouterModel[]

        if (models.length > 0) {
          updateInfo = {
            version: latestVersion,
            timestamp: new Date().toISOString()
          }

          // Update stored version
          await modelUpdateLogic.updateStoredVersion(latestVersion)
          console.log(`Updated to model manifest v${latestVersion}`)
        }
      } else {
        console.log('Using existing models or fetching from OpenRouter...')
        const enhancedModels = await modelUpdateLogic.getModels(settings.openRouterApiKey)
        models = enhancedModels as OpenRouterModel[]
      }

      set({ models })
      console.log(`Loaded ${models.length} models`)

      // Show toast notification for model updates
      if (updateInfo) {
        const updateTime = new Date(updateInfo.timestamp).toLocaleString()

        // Calculate model statistics for summary
        const flagshipCount = models.filter(m => m.categories?.includes('flagship')).length
        const freeCount = models.filter(m => m.categories?.includes('free')).length
        const visionCount = models.filter(m => m.categories?.includes('vision')).length
        const reasoningCount = models.filter(m => m.categories?.includes('reasoning')).length
        const codeCount = models.filter(m => m.categories?.includes('code')).length
        const searchCount = models.filter(m => m.categories?.includes('search')).length

        // Dispatch custom event for toast notification with summary data
        window.dispatchEvent(new CustomEvent('model-update-complete', {
          detail: {
            message: `Models updated to v${updateInfo.version}`,
            type: 'success',
            updateTime,
            summary: {
              total: models.length,
              flagship: flagshipCount,
              free: freeCount,
              vision: visionCount,
              reasoning: reasoningCount,
              code: codeCount,
              search: searchCount
            }
          }
        }))
      }

      // Debug: Check for latest models including new ones
      const latestModels = models.filter(m =>
        m.id.includes('grok-4') ||
        m.id.includes('gemini-2.5') ||
        m.id.includes('claude-3.5') ||
        m.id.includes('chatgpt-4o-latest') ||
        m.name.toLowerCase().includes('grok 4') ||
        m.name.toLowerCase().includes('gemini 2.5')
      )
      console.log('Latest models found:', latestModels.map(m => ({ id: m.id, name: m.name })))
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  },

  searchConversations: async (term: string) => {
    try {
      if (!term.trim()) {
        get().loadConversations()
        return
      }
      const conversations = await window.electronAPI.db.searchConversations(term)
      set({ conversations })
    } catch (error) {
      console.error('Failed to search conversations:', error)
    }
  },

  togglePinMessage: async (messageId: string) => {
    try {
      await window.electronAPI.db.togglePinMessage(messageId)
      const { currentConversationId } = get()
      if (currentConversationId) {
        await get().loadMessages(currentConversationId)
      }
    } catch (error) {
      console.error('Failed to toggle pin on message:', error)
    }
  },
}))

// Load initial data
export const initializeApp = async () => {
  // Wait for electron API to be available
  if (typeof window !== 'undefined' && window.electronAPI) {
    const store = useAppStore.getState()
    await store.loadConversations()

    // Load settings
    try {
      const savedSettings = await window.electronAPI.settings.get('app-settings')
      if (savedSettings) {
        store.updateSettings(savedSettings)

        // Auto-load models if API key exists
        if (savedSettings.openRouterApiKey) {
          console.log('Auto-loading models with saved API key...')
          await store.loadModels()
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }

    // Always check for local models on app start
    const networkStore = useNetworkStore.getState()
    await networkStore.checkLocalModels()
  } else {
    console.log('Running in browser mode - electron API not available')
  }
}
