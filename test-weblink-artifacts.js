/**
 * Test Web Link Artifact Detection
 */

// Import the artifact detection function
const { detectArtifacts } = require('./src/types/artifacts.ts')

// Test message with various web links
const testMessage = `
Here are some useful resources:

1. OpenAI's website: https://openai.com
2. GitHub repository: https://github.com/microsoft/vscode
3. Documentation: https://docs.anthropic.com/claude/docs
4. News article: https://www.bbc.com/news/technology-12345678
5. Research paper: https://arxiv.org/abs/2301.00001

You can also check out:
- https://stackoverflow.com/questions/12345/how-to-code
- https://www.youtube.com/watch?v=dQw4w9WgXcQ
- https://medium.com/@author/article-title-123abc

Let me know if you need more information!
`

console.log('🔗 Testing Web Link Artifact Detection')
console.log('=====================================\n')

try {
  const result = detectArtifacts(testMessage, 'test-message-123')
  
  console.log(`📊 Detection Results:`)
  console.log(`   Total artifacts found: ${result.artifacts.length}`)
  console.log(`   Web link artifacts: ${result.artifacts.filter(a => a.type === 'weblink').length}`)
  console.log('')
  
  console.log('🌐 Web Link Artifacts:')
  result.artifacts
    .filter(artifact => artifact.type === 'weblink')
    .forEach((artifact, index) => {
      console.log(`\n   ${index + 1}. ${artifact.title}`)
      console.log(`      URL: ${artifact.content}`)
      console.log(`      Domain: ${artifact.metadata.domain}`)
      console.log(`      Favicon: ${artifact.metadata.favicon}`)
    })
  
  console.log('\n📝 Processed Content:')
  console.log('---')
  console.log(result.processedContent)
  console.log('---')
  
} catch (error) {
  console.error('❌ Error testing artifact detection:', error)
}

console.log('\n✅ Web link artifact detection test completed!')
